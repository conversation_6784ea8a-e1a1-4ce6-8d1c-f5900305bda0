# Deployment Guide

This guide covers different deployment options for the School Management System.

## Prerequisites

- Node.js 18+ installed
- PostgreSQL database
- Environment variables configured

## Environment Variables

Create a `.env.local` file with the following variables:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/school_management"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Email Configuration (Optional)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
SMTP_SECURE="false"

# File Upload
MAX_FILE_SIZE="5242880"

# Redis (Optional)
REDIS_URL="redis://localhost:6379"
```

## Local Development

1. Install dependencies:
```bash
npm install
```

2. Set up the database:
```bash
npx prisma migrate dev
npx prisma db seed
```

3. Start the development server:
```bash
npm run dev
```

## Docker Deployment

### Using Docker Compose (Recommended)

1. Clone the repository:
```bash
git clone <repository-url>
cd school-management-system
```

2. Create environment file:
```bash
cp .env.example .env.local
# Edit .env.local with your configuration
```

3. Start all services:
```bash
docker-compose up -d
```

4. Run database migrations:
```bash
docker-compose exec app npx prisma migrate deploy
docker-compose exec app npx prisma db seed
```

5. Access the application at `http://localhost:3000`

### Using Docker only

1. Build the image:
```bash
docker build -t school-management .
```

2. Run the container:
```bash
docker run -p 3000:3000 \
  -e DATABASE_URL="your-database-url" \
  -e NEXTAUTH_URL="http://localhost:3000" \
  -e NEXTAUTH_SECRET="your-secret" \
  school-management
```

## Vercel Deployment

1. Install Vercel CLI:
```bash
npm i -g vercel
```

2. Login to Vercel:
```bash
vercel login
```

3. Deploy:
```bash
vercel --prod
```

4. Set environment variables in Vercel dashboard:
   - Go to your project settings
   - Add all required environment variables
   - Redeploy if necessary

### Database Setup for Vercel

1. Use a cloud PostgreSQL service (recommended):
   - Neon (https://neon.tech)
   - Supabase (https://supabase.com)
   - PlanetScale (https://planetscale.com)
   - Railway (https://railway.app)

2. Update `DATABASE_URL` in Vercel environment variables

3. Run migrations:
```bash
npx prisma migrate deploy
```

## Production Considerations

### Security

1. **Environment Variables**: Never commit sensitive data to version control
2. **HTTPS**: Always use HTTPS in production
3. **Database Security**: Use strong passwords and restrict access
4. **CORS**: Configure proper CORS settings
5. **Rate Limiting**: Implement rate limiting for API endpoints

### Performance

1. **Database Optimization**:
   - Add proper indexes
   - Use connection pooling
   - Monitor query performance

2. **Caching**:
   - Enable Redis for session storage
   - Implement API response caching
   - Use CDN for static assets

3. **Monitoring**:
   - Set up error tracking (Sentry)
   - Monitor application performance
   - Set up health checks

### Backup Strategy

1. **Database Backups**:
   - Daily automated backups
   - Test restore procedures
   - Store backups securely

2. **File Backups**:
   - Backup uploaded files
   - Use cloud storage for redundancy

### Scaling

1. **Horizontal Scaling**:
   - Use load balancers
   - Deploy multiple app instances
   - Use managed database services

2. **Vertical Scaling**:
   - Monitor resource usage
   - Scale server resources as needed

## Maintenance

### Regular Tasks

1. **Database Maintenance**:
```bash
# Clean up old audit logs (run monthly)
npx prisma db execute --file=./scripts/cleanup-audit-logs.sql

# Optimize database
npx prisma db execute --file=./scripts/optimize-db.sql
```

2. **File Cleanup**:
```bash
# Clean up old temporary files
npm run cleanup:files
```

3. **Security Updates**:
```bash
# Update dependencies
npm audit fix
npm update
```

### Monitoring Commands

```bash
# Check application health
curl http://localhost:3000/api/health

# Check database connection
npx prisma db execute --stdin < scripts/health-check.sql

# View logs
docker-compose logs -f app
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**:
   - Check DATABASE_URL format
   - Verify database server is running
   - Check network connectivity

2. **Authentication Issues**:
   - Verify NEXTAUTH_SECRET is set
   - Check NEXTAUTH_URL matches your domain
   - Clear browser cookies

3. **File Upload Issues**:
   - Check file permissions
   - Verify upload directory exists
   - Check MAX_FILE_SIZE setting

4. **Email Issues**:
   - Verify SMTP credentials
   - Check firewall settings
   - Test with a simple email client

### Logs and Debugging

1. **Application Logs**:
```bash
# Docker logs
docker-compose logs app

# PM2 logs (if using PM2)
pm2 logs school-app
```

2. **Database Logs**:
```bash
# PostgreSQL logs
docker-compose logs postgres
```

3. **Debug Mode**:
```bash
# Enable debug logging
DEBUG=* npm run dev
```

## Support

For technical support or questions:
- Check the documentation in `/docs`
- Review the troubleshooting section
- Contact the development team

## License

This project is licensed under the MIT License. See LICENSE file for details.
