version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: school-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: school_management
      POSTGRES_USER: school_user
      POSTGRES_PASSWORD: school_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - school-network

  # Redis for session storage and caching
  redis:
    image: redis:7-alpine
    container_name: school-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - school-network

  # Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: school-app
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: ******************************************************/school_management
      
      # NextAuth
      NEXTAUTH_URL: http://localhost:3000
      NEXTAUTH_SECRET: your-secret-key-here
      
      # Email Configuration (optional)
      SMTP_HOST: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_USER: <EMAIL>
      SMTP_PASSWORD: your-app-password
      SMTP_SECURE: false
      
      # Redis (optional)
      REDIS_URL: redis://redis:6379
      
      # File Upload
      MAX_FILE_SIZE: 5242880
      
      # Application
      NODE_ENV: production
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
    networks:
      - school-network
    volumes:
      - ./public/uploads:/app/public/uploads
      - ./public/reports:/app/public/reports

  # Nginx Reverse Proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: school-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - school-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  school-network:
    driver: bridge
