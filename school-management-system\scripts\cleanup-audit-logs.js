const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

/**
 * Cleanup old audit logs to maintain database performance
 */
async function cleanupAuditLogs() {
  try {
    console.log('Starting audit log cleanup...')

    // Keep logs for 1 year (365 days)
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - 365)

    console.log(`Deleting audit logs older than ${cutoffDate.toISOString()}`)

    const result = await prisma.auditLog.deleteMany({
      where: {
        timestamp: {
          lt: cutoffDate
        }
      }
    })

    console.log(`Deleted ${result.count} old audit log entries`)

    // Get current audit log count
    const remainingCount = await prisma.auditLog.count()
    console.log(`Remaining audit logs: ${remainingCount}`)

    // Get oldest remaining log
    const oldestLog = await prisma.auditLog.findFirst({
      orderBy: { timestamp: 'asc' },
      select: { timestamp: true }
    })

    if (oldestLog) {
      console.log(`Oldest remaining log: ${oldestLog.timestamp.toISOString()}`)
    }

  } catch (error) {
    console.error('Error cleaning up audit logs:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run cleanup if called directly
if (require.main === module) {
  cleanupAuditLogs()
}

module.exports = { cleanupAuditLogs }
