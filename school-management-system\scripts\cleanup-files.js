const fs = require('fs')
const path = require('path')

/**
 * Cleanup old temporary files and reports
 */
async function cleanupFiles() {
  const publicDir = path.join(process.cwd(), 'public')
  const uploadsDir = path.join(publicDir, 'uploads')
  const reportsDir = path.join(publicDir, 'reports')
  
  let totalDeleted = 0

  console.log('Starting file cleanup...')

  // Clean up temporary uploads older than 24 hours
  if (fs.existsSync(path.join(uploadsDir, 'temp'))) {
    const tempFiles = await cleanupDirectory(path.join(uploadsDir, 'temp'), 24)
    totalDeleted += tempFiles
    console.log(`Cleaned up ${tempFiles} temporary files`)
  }

  // Clean up old reports older than 30 days
  if (fs.existsSync(reportsDir)) {
    const reportFiles = await cleanupDirectory(reportsDir, 30 * 24)
    totalDeleted += reportFiles
    console.log(`Cleaned up ${reportFiles} old report files`)
  }

  console.log(`Total files deleted: ${totalDeleted}`)
}

async function cleanupDirectory(dirPath, maxAgeHours) {
  const files = fs.readdirSync(dirPath)
  const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000)
  let deletedCount = 0

  for (const file of files) {
    const filePath = path.join(dirPath, file)
    const stats = fs.statSync(filePath)
    
    if (stats.mtime.getTime() < cutoffTime) {
      try {
        fs.unlinkSync(filePath)
        deletedCount++
        console.log(`Deleted: ${filePath}`)
      } catch (error) {
        console.error(`Error deleting ${filePath}:`, error.message)
      }
    }
  }

  return deletedCount
}

// Run cleanup if called directly
if (require.main === module) {
  cleanupFiles().catch(console.error)
}

module.exports = { cleanupFiles }
