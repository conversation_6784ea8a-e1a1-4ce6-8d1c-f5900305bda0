import * as XLSX from 'xlsx';
import * as fs from 'fs';
import * as path from 'path';

// Create the Excel template for student import
function generateExcelTemplate() {
  // Define the headers
  const headers = [
    'First Name',
    'Last Name', 
    'Email',
    'Date of Birth',
    'Gender',
    'Phone Number',
    'Address',
    'Guardian Name',
    'Guardian Phone',
    'Class',
    'Section',
    'Roll Number',
    'Academic Year',
    'Admission Number'
  ];

  // Define example data
  const exampleData = [
    [
      '<PERSON>',
      'Do<PERSON>',
      '<EMAIL>',
      '2010-06-15',
      'MALE',
      '+1234567890',
      '123 Main Street, City, State 12345',
      '<PERSON>',
      '+1234567891',
      'Grade 8',
      'A',
      '8A01',
      '2024-2025',
      'ADM0001'
    ],
    [
      '<PERSON>',
      '<PERSON>',
      '<EMAIL>',
      '2010-08-22',
      'FEMALE',
      '+1234567892',
      '456 Oak Avenue, City, State 12345',
      '<PERSON>',
      '+1234567893',
      'Grade 8',
      'A',
      '8A02',
      '2024-2025',
      'ADM0002'
    ],
    [
      '<PERSON>',
      '<PERSON>',
      'micha<PERSON>.<EMAIL>',
      '2010-04-10',
      'MALE',
      '+1234567894',
      '789 Pine Road, City, State 12345',
      'Lisa Johnson',
      '+1234567895',
      'Grade 8',
      'B',
      '8B01',
      '2024-2025',
      'ADM0003'
    ],
    [
      'Emily',
      'Brown',
      '<EMAIL>',
      '2010-12-03',
      'FEMALE',
      '+1234567896',
      '321 Elm Street, City, State 12345',
      'David Brown',
      '+1234567897',
      'Grade 8',
      'B',
      '8B02',
      '2024-2025',
      'ADM0004'
    ]
  ];

  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new();
  const worksheetData = [headers, ...exampleData];
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths
  const columnWidths = [
    { wch: 15 }, // First Name
    { wch: 15 }, // Last Name
    { wch: 25 }, // Email
    { wch: 12 }, // Date of Birth
    { wch: 8 },  // Gender
    { wch: 15 }, // Phone Number
    { wch: 35 }, // Address
    { wch: 20 }, // Guardian Name
    { wch: 15 }, // Guardian Phone
    { wch: 10 }, // Class
    { wch: 8 },  // Section
    { wch: 12 }, // Roll Number
    { wch: 12 }, // Academic Year
    { wch: 15 }  // Admission Number
  ];
  worksheet['!cols'] = columnWidths;

  // Add the worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Student Import Template');

  // Create templates directory if it doesn't exist
  const templatesDir = path.join(process.cwd(), 'templates');
  if (!fs.existsSync(templatesDir)) {
    fs.mkdirSync(templatesDir, { recursive: true });
  }

  // Write the Excel file
  const filePath = path.join(templatesDir, 'student-import-template.xlsx');
  XLSX.writeFile(workbook, filePath);

  console.log(`✅ Excel template created successfully at: ${filePath}`);
}

// Run the script
generateExcelTemplate();
