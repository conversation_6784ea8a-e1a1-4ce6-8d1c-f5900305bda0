'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { getGradeBadgeColor } from '@/lib/grading'
import { 
  Calendar, 
  Award, 
  BookOpen, 
  Users, 
  TrendingUp,
  FileText,
  Download,
  Filter,
  Plus,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface MarkRecord {
  id: string
  obtainedMarks: number
  remarks?: string
  createdAt: string
  updatedAt: string
  student: {
    id: string
    admissionNo: string
    rollNumber: string
    user: {
      firstName: string
      lastName: string
      email: string
    }
    currentClass: {
      name: string
    }
    currentSection: {
      name: string
    }
  }
  exam: {
    id: string
    name: string
    maxMarks: number
    date: string
    subject: {
      id: string
      name: string
      code: string
    }
    term: {
      id: string
      name: string
    }
  }
  gradedByTeacher?: {
    id: string
    user: {
      firstName: string
      lastName: string
    }
  }
}

import { adminNavigation } from '@/lib/navigation';

export default function MarksPage() {
  const [markRecords, setMarkRecords] = useState<MarkRecord[]>([])
  const [selectedExam, setSelectedExam] = useState('all')
  const [selectedSubject, setSelectedSubject] = useState('all')
  const [selectedClass, setSelectedClass] = useState('all')
  const [loading, setLoading] = useState(true)
  const [terms, setTerms] = useState<Array<{id: string, name: string}>>([])
  const [subjects, setSubjects] = useState<Array<{id: string, name: string, class: {name: string}}>>([])
  const [classes, setClasses] = useState<Array<{id: number, name: string}>>([])

  useEffect(() => {
    fetchMarks()
    fetchFiltersData()
  }, [selectedExam, selectedSubject, selectedClass])

  const fetchMarks = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (selectedExam !== 'all') params.append('examId', selectedExam)
      if (selectedSubject !== 'all') params.append('subjectId', selectedSubject)
      if (selectedClass !== 'all') params.append('classId', selectedClass)

      const response = await fetch(`/api/admin/marks?${params}`)
      if (response.ok) {
        const data = await response.json()
        setMarkRecords(data)
      } else {
        console.error('Failed to fetch marks')
      }
    } catch (error) {
      console.error('Error fetching marks:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchFiltersData = async () => {
    try {
      const [termsResponse, subjectsResponse, classesResponse] = await Promise.all([
        fetch('/api/admin/terms'),
        fetch('/api/admin/subjects'),
        fetch('/api/admin/classes')
      ])

      if (termsResponse.ok) {
        const termsData = await termsResponse.json()
        setTerms(termsData)
      }

      if (subjectsResponse.ok) {
        const subjectsData = await subjectsResponse.json()
        setSubjects(subjectsData.subjects || subjectsData)
      }

      if (classesResponse.ok) {
        const classesData = await classesResponse.json()
        setClasses(classesData.classes || classesData)
      }
    } catch (error) {
      console.error('Error fetching filter data:', error)
    }
  }



  const marksWithCalculations = markRecords.map(record => {
    const percentage = Math.round((record.obtainedMarks / record.exam.maxMarks) * 100 * 100) / 100
    const grade = getGrade(percentage)
    return {
      ...record,
      percentage,
      grade
    }
  })

  const getGrade = (percentage: number) => {
    if (percentage >= 90) return 'A+'
    if (percentage >= 80) return 'A'
    if (percentage >= 70) return 'B+'
    if (percentage >= 60) return 'B'
    if (percentage >= 50) return 'C+'
    if (percentage >= 40) return 'C'
    if (percentage >= 30) return 'D'
    return 'F'
  }

  const marksStats = {
    total: marksWithCalculations.length,
    average: marksWithCalculations.length > 0
      ? Math.round(marksWithCalculations.reduce((sum, record) => sum + record.percentage, 0) / marksWithCalculations.length)
      : 0,
    highest: marksWithCalculations.length > 0 ? Math.max(...marksWithCalculations.map(r => r.percentage)) : 0,
    lowest: marksWithCalculations.length > 0 ? Math.min(...marksWithCalculations.map(r => r.percentage)) : 0,
    passed: marksWithCalculations.filter(r => r.percentage >= 40).length,
    failed: marksWithCalculations.filter(r => r.percentage < 40).length
  }

  return (
    <DashboardLayout title="Marks Management" navigation={adminNavigation}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Marks Management</h1>
            <p className="text-gray-600">Monitor and manage student examination marks</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export Marks
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Marks
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
          <Card key="total-marks-records">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Records</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{marksStats.total}</div>
            </CardContent>
          </Card>
          <Card key="average-marks-score">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{marksStats.average}%</div>
            </CardContent>
          </Card>
          <Card key="highest-marks-score">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Highest Score</CardTitle>
              <Award className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{marksStats.highest}%</div>
            </CardContent>
          </Card>
          <Card key="lowest-marks-score">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Lowest Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{marksStats.lowest}%</div>
            </CardContent>
          </Card>
          <Card key="passed-students">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Passed</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{marksStats.passed}</div>
            </CardContent>
          </Card>
          <Card key="failed-students">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{marksStats.failed}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="subject">Subject</Label>
                <select
                  id="subject"
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Subjects</option>
                  {subjects.map(subject => (
                    <option key={subject.id} value={subject.id}>
                      {subject.name} ({subject.class.name})
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="class">Class</Label>
                <select
                  id="class"
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Classes</option>
                  {classes.map(cls => (
                    <option key={cls.id} value={cls.id.toString()}>{cls.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="search">Search Student</Label>
                <Input
                  id="search"
                  type="text"
                  placeholder="Search by name or admission no..."
                  className="w-full"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Marks Table */}
        <Card>
          <CardHeader>
            <CardTitle>Marks Records</CardTitle>
            <CardDescription>
              Examination marks and grades for all students
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading marks...</p>
              </div>
            ) : marksWithCalculations.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No marks found</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Student
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Exam
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Subject
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Class
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Marks
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Percentage
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Grade
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Graded By
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {marksWithCalculations.filter(record => record?.id).map((record) => (
                      <tr key={record.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                <span className="text-sm font-medium text-gray-700">
                                  {record.student.user.firstName[0]}{record.student.user.lastName[0]}
                                </span>
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {record.student.user.firstName} {record.student.user.lastName}
                              </div>
                              <div className="text-sm text-gray-500">{record.student.admissionNo}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.exam.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.exam.subject.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.student.currentClass.name} - {record.student.currentSection.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.obtainedMarks}/{record.exam.maxMarks}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.percentage}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeBadgeColor(record.grade)}`}>
                            {record.grade}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.gradedByTeacher
                            ? `${record.gradedByTeacher.user.firstName} ${record.gradedByTeacher.user.lastName}`
                            : 'System'
                          }
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              Edit
                            </Button>
                            <Button variant="outline" size="sm">
                              View
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
