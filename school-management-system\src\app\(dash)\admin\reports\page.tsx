'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { getGradeBadgeColor } from '@/lib/grading'
import {
  Calendar,
  FileText,
  Download,
  BarChart3,
  Users,
  Award,
  TrendingUp,
  Printer,
  Eye,
  Loader2,
  AlertCircle,
  Plus
} from 'lucide-react'

interface ReportCard {
  id: string
  studentId: string
  termId: string
  jsonSnapshot: any
  pdfPath?: string
  generatedAt: string
  student: {
    admissionNo: string
    rollNumber: string
    user: {
      firstName: string
      lastName: string
    }
    currentClass: {
      name: string
    }
    currentSection: {
      name: string
    }
  }
  term: {
    name: string
    academicYear: string
  }
}

interface Term {
  id: string
  name: string
  academicYear: string
}

interface Class {
  id: string
  name: string
}

import { adminNavigation } from '@/lib/navigation';

export default function ReportsPage() {
  const [reportCards, setReportCards] = useState<ReportCard[]>([])
  const [terms, setTerms] = useState<Term[]>([])
  const [classes, setClasses] = useState<Class[]>([])
  const [selectedTerm, setSelectedTerm] = useState('all')
  const [selectedClass, setSelectedClass] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [loading, setLoading] = useState(true)
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchReportCards()
    fetchTermsAndClasses()
  }, [selectedTerm, selectedClass, selectedStatus])

  const fetchReportCards = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (selectedTerm !== 'all') params.append('termId', selectedTerm)
      if (selectedClass !== 'all') params.append('classId', selectedClass)
      if (selectedStatus !== 'all') params.append('status', selectedStatus)

      const response = await fetch(`/api/admin/reports?${params}`)
      if (response.ok) {
        const data = await response.json()
        setReportCards(data)
      } else {
        setError('Failed to fetch report cards')
      }
    } catch (error) {
      console.error('Error fetching report cards:', error)
      setError('Error fetching report cards')
    } finally {
      setLoading(false)
    }
  }

  const fetchTermsAndClasses = async () => {
    try {
      const [termsResponse, classesResponse] = await Promise.all([
        fetch('/api/admin/exams?type=terms'),
        fetch('/api/admin/classes')
      ])

      if (termsResponse.ok) {
        const termsData = await termsResponse.json()
        setTerms(termsData)
      }

      if (classesResponse.ok) {
        const classesData = await classesResponse.json()
        setClasses(classesData.classes || [])
      }
    } catch (error) {
      console.error('Error fetching terms and classes:', error)
    }
  }

  const handleGenerateReports = async () => {
    if (selectedTerm === 'all' || selectedClass === 'all') {
      setError('Please select both term and class to generate reports')
      return
    }

    try {
      setGenerating(true)
      setError(null)

      const response = await fetch('/api/admin/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          termId: selectedTerm,
          classId: selectedClass
        })
      })

      if (response.ok) {
        await fetchReportCards() // Refresh the list
        setError(null)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to generate reports')
      }
    } catch (error) {
      console.error('Error generating reports:', error)
      setError('Error generating reports')
    } finally {
      setGenerating(false)
    }
  }

  const handleDownloadPDF = async (reportCardId: string, studentName: string) => {
    try {
      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'report-card',
          reportCardId: reportCardId
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `report-card-${studentName.replace(/\s+/g, '-')}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        setError('Failed to download PDF')
      }
    } catch (error) {
      console.error('Error downloading PDF:', error)
      setError('Error downloading PDF')
    }
  }

  const handleDownloadClassReport = async () => {
    if (selectedTerm === 'all' || selectedClass === 'all') {
      setError('Please select both term and class to download class report')
      return
    }

    try {
      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'class-report',
          termId: selectedTerm,
          classId: selectedClass
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `class-report-${selectedTerm}-${selectedClass}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        setError('Failed to download class report')
      }
    } catch (error) {
      console.error('Error downloading class report:', error)
      setError('Error downloading class report')
    }
  }

  // Calculate statistics
  const stats = {
    total: reportCards.length,
    generated: reportCards.filter(r => r.pdfPath).length,
    pending: reportCards.filter(r => !r.pdfPath).length,
    averagePercentage: reportCards.length > 0
      ? Math.round(reportCards.reduce((sum, r) => sum + ((r.jsonSnapshot as any)?.summary?.percentage || 0), 0) / reportCards.length)
      : 0
  }
  const getStatusColor = (hasPdf: boolean) => {
    return hasPdf
      ? 'bg-green-100 text-green-800'
      : 'bg-yellow-100 text-yellow-800'
  }

  const getStatusText = (hasPdf: boolean) => {
    return hasPdf ? 'Generated' : 'Pending'
  }





  return (
    <DashboardLayout title="Reports Management" navigation={adminNavigation}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Reports Management</h1>
            <p className="text-sm sm:text-base text-gray-600">Generate and manage student report cards</p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
            <Button
              variant="outline"
              className="w-full sm:w-auto"
              onClick={handleDownloadClassReport}
              disabled={selectedTerm === 'all' || selectedClass === 'all'}
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              <span className="sm:hidden">Class Report</span>
              <span className="hidden sm:inline">Download Class Report</span>
            </Button>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Generated PDFs</CardTitle>
              <Award className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.generated}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending PDFs</CardTitle>
              <Award className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stats.pending}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Class Average</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.averagePercentage}%</div>
            </CardContent>
          </Card>
        </div>

        {/* Report Generation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              Generate Report Cards
            </CardTitle>
            <CardDescription>
              Create report cards for all students in a class
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="term-select">Select Term</Label>
                <select
                  id="term-select"
                  value={selectedTerm}
                  onChange={(e) => setSelectedTerm(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">Choose a term</option>
                  {terms.filter(term => term?.id).map(term => (
                    <option key={term.id} value={term.id}>{term.name} ({term.academicYear})</option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="class-select">Select Class</Label>
                <select
                  id="class-select"
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">Choose a class</option>
                  {classes.filter(cls => cls?.id).map(cls => (
                    <option key={cls.id} value={cls.id}>{cls.name}</option>
                  ))}
                </select>
              </div>
              <div className="flex items-end">
                <Button
                  className="w-full"
                  onClick={handleGenerateReports}
                  disabled={generating || selectedTerm === 'all' || selectedClass === 'all'}
                >
                  {generating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      Generate Reports
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="term">Term</Label>
                <select
                  id="term"
                  value={selectedTerm}
                  onChange={(e) => setSelectedTerm(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Terms</option>
                  {terms.filter(term => term?.id).map(term => (
                    <option key={term.id} value={term.id}>{term.name} ({term.academicYear})</option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="class">Class</Label>
                <select
                  id="class"
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Classes</option>
                  {classes.filter(cls => cls?.id).map(cls => (
                    <option key={cls.id} value={cls.id}>{cls.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="status">Status</Label>
                <select
                  id="status"
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="generated">Generated</option>
                  <option value="pending">Pending</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Report Cards Table */}
        <Card>
          <CardHeader>
            <CardTitle>Report Cards</CardTitle>
            <CardDescription>
              Generated report cards for students
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <Loader2 className="h-8 w-8 animate-spin mx-auto" />
                <p className="mt-2 text-gray-600">Loading report cards...</p>
              </div>
            ) : reportCards.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No report cards found</p>
                <p className="text-sm text-gray-500 mt-2">Generate reports for a class to see them here</p>
              </div>
            ) : (
              <>
                {/* Desktop Table */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Student
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Class
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Term
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Percentage
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Grade
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {reportCards.filter(card => card?.id).map((card) => {
                        const summary = (card.jsonSnapshot as any)?.summary || {}
                        const studentName = `${card.student.user.firstName} ${card.student.user.lastName}`

                        return (
                          <tr key={card.id}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10">
                                  <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                    <span className="text-sm font-medium text-gray-700">
                                      {card.student.user.firstName[0]}{card.student.user.lastName[0]}
                                    </span>
                                  </div>
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">{studentName}</div>
                                  <div className="text-sm text-gray-500">{card.student.admissionNo}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {card.student.currentClass.name} - {card.student.currentSection.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {card.term.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {summary.percentage ? `${summary.percentage.toFixed(1)}%` : 'N/A'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeBadgeColor(summary.grade || 'F')}`}>
                                {summary.grade || 'N/A'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(!!card.pdfPath)}`}>
                                {getStatusText(!!card.pdfPath)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDownloadPDF(card.id, studentName)}
                                >
                                  <Download className="w-4 h-4" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>

                {/* Mobile Cards */}
                <div className="lg:hidden space-y-4">
                  {reportCards.filter(card => card?.id).map((card) => {
                    const summary = (card.jsonSnapshot as any)?.summary || {}
                    const studentName = `${card.student.user.firstName} ${card.student.user.lastName}`

                    return (
                      <Card key={card.id} className="p-4">
                        <div className="flex flex-col space-y-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0">
                                <span className="text-sm font-medium text-gray-700">
                                  {card.student.user.firstName[0]}{card.student.user.lastName[0]}
                                </span>
                              </div>
                              <div className="min-w-0 flex-1">
                                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
                                  {studentName}
                                </h3>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  {card.student.admissionNo} • {card.student.currentClass.name} - {card.student.currentSection.name}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(!!card.pdfPath)}`}>
                                {getStatusText(!!card.pdfPath)}
                              </span>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium text-gray-700 dark:text-gray-300">Term:</span>
                              <p className="text-gray-600 dark:text-gray-400">{card.term.name}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700 dark:text-gray-300">Percentage:</span>
                              <p className="text-gray-600 dark:text-gray-400">{summary.percentage ? `${summary.percentage.toFixed(1)}%` : 'N/A'}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700 dark:text-gray-300">Grade:</span>
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeBadgeColor(summary.grade || 'F')}`}>
                                {summary.grade || 'N/A'}
                              </span>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700 dark:text-gray-300">Generated:</span>
                              <p className="text-gray-600 dark:text-gray-400">{new Date(card.generatedAt).toLocaleDateString()}</p>
                            </div>
                          </div>

                          <div className="flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex-1"
                              onClick={() => handleDownloadPDF(card.id, studentName)}
                            >
                              <Download className="w-4 h-4 mr-1" />
                              Download
                            </Button>
                          </div>
                        </div>
                      </Card>
                    )
                  })}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
