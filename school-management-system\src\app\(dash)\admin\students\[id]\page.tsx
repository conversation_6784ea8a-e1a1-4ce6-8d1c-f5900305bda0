import { getServerSession } from 'next-auth';
import { redirect, notFound } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { prisma as db } from '@/lib/db';
import { hasPermission } from '@/lib/rbac';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { adminNavigation } from '@/lib/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  MapPin, 
  BookOpen, 
  Edit, 
  ArrowLeft,
  GraduationCap,
  Users,
  Clock,
  Award
} from 'lucide-react';
import Link from 'next/link';

interface StudentDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function StudentDetailsPage({ params }: StudentDetailsPageProps) {
  // Temporarily bypass authentication for testing
  // const session = await getServerSession(authOptions);

  // if (!session?.user) {
  //   redirect('/login');
  // }

  // if (!hasPermission(session.user.role as any, 'students:read')) {
  //   redirect('/unauthorized');
  // }

  const { id } = await params;

  // Fetch student with all related data
  const student = await db.student.findUnique({
    where: { id },
    include: {
      user: true,
      currentClass: {
        include: {
          sections: true,
        },
      },
      currentSection: true,
      enrollments: {
        include: {
          class: true,
          section: true,
        },
      },
      attendances: {
        take: 10,
        orderBy: { date: 'desc' },
      },
      marks: {
        include: {
          exam: {
            include: {
              subject: true,
            },
          },
        },
        take: 20,
        orderBy: { createdAt: 'desc' },
      },
    },
  });

  if (!student) {
    notFound();
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  const getGenderLabel = (gender: string) => {
    switch (gender) {
      case 'MALE': return 'Male';
      case 'FEMALE': return 'Female';
      case 'OTHER': return 'Other';
      default: return gender;
    }
  };

  const getAttendanceStatus = (status: string) => {
    switch (status) {
      case 'PRESENT': return { label: 'Present', color: 'bg-green-100 text-green-800' };
      case 'ABSENT': return { label: 'Absent', color: 'bg-red-100 text-red-800' };
      case 'LATE': return { label: 'Late', color: 'bg-yellow-100 text-yellow-800' };
      case 'HALF_DAY': return { label: 'Half Day', color: 'bg-orange-100 text-orange-800' };
      default: return { label: status, color: 'bg-gray-100 text-gray-800' };
    }
  };

  const calculateAge = (dateOfBirth: Date) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  const attendanceStats = student.attendances.reduce((acc, record) => {
    acc[record.status] = (acc[record.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const totalAttendance = student.attendances.length;
  const presentCount = attendanceStats['PRESENT'] || 0;
  const attendancePercentage = totalAttendance > 0 ? Math.round((presentCount / totalAttendance) * 100) : 0;

  return (
    <DashboardLayout 
      title="Student Details"
      navigation={adminNavigation}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/admin/students">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Students
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {student.user.firstName} {student.user.lastName}
              </h1>
              <p className="text-muted-foreground">
                Student ID: {student.id}
              </p>
            </div>
          </div>
          <Link href={`/admin/students/${student.id}/edit`}>
            <Button>
              <Edit className="w-4 h-4 mr-2" />
              Edit Student
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Personal Information */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Full Name</label>
                    <p className="text-lg font-medium">
                      {student.user.firstName} {student.user.lastName}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Email</label>
                    <p className="text-lg font-medium">
                      {student.user.email}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Date of Birth</label>
                    <p className="text-lg font-medium">
                      {formatDate(student.dob)} ({calculateAge(student.dob)} years old)
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Gender</label>
                    <p className="text-lg font-medium">
                      {getGenderLabel(student.gender)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Phone Number</label>
                    <p className="text-lg font-medium">
                      {student.user.phone || 'Not provided'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Address</label>
                    <p className="text-lg font-medium">
                      {student.address || 'Not provided'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Academic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <GraduationCap className="w-5 h-5" />
                  Academic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Current Class</label>
                    <p className="flex items-center gap-2">
                      <BookOpen className="w-4 h-4" />
                      {student.currentClass ? `${student.currentClass.name} - ${student.currentSection?.name || 'N/A'}` : 'Not assigned'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Enrolled Subjects</label>
                    <p className="flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      {student.enrollments.length} subjects
                    </p>
                  </div>
                </div>
                
                {student.enrollments.length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Enrollment History</label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {student.enrollments.map((enrollment) => (
                        <Badge key={enrollment.id} variant="secondary">
                          {enrollment.class.name} - {enrollment.section.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Marks */}
            {student.marks.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    Recent Marks
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {student.marks.slice(0, 5).map((mark) => (
                      <div key={mark.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{mark.exam.subject.name}</p>
                          <p className="text-sm text-gray-600">{mark.exam.name}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-lg">{mark.obtainedMarks}/{mark.exam.maxMarks}</p>
                          <p className="text-sm text-gray-600">
                            {Math.round((mark.obtainedMarks / mark.exam.maxMarks) * 100)}%
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Guardian Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Guardian Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <label className="text-sm font-medium text-gray-600">Guardian Name</label>
                  <p>{student.guardianName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Guardian Phone</label>
                  <p className="flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    {student.guardianPhone}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Attendance Summary */}
            {student.attendances.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Attendance Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">{attendancePercentage}%</div>
                    <div className="text-sm text-gray-600">Attendance Rate</div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Present</span>
                      <span className="font-medium">{presentCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Absent</span>
                      <span className="font-medium">{attendanceStats['ABSENT'] || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Late</span>
                      <span className="font-medium">{attendanceStats['LATE'] || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Total Days</span>
                      <span className="font-medium">{totalAttendance}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Recent Attendance */}
            {student.attendances.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Recent Attendance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {student.attendances.filter(record => record?.id).slice(0, 5).map((record) => {
                      const status = getAttendanceStatus(record.status);
                      return (
                        <div key={record.id} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4 text-gray-400" />
                            <span className="text-sm">{formatDate(record.date)}</span>
                          </div>
                          <Badge className={status.color}>
                            {status.label}
                          </Badge>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
