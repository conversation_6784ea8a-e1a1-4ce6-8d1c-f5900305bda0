'use client'

import { useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { studentNavigation } from '@/lib/navigation'
import {
  TrendingUp,
  Bell,
  GraduationCap,
  Target,
  Award,
  Calendar,
  FileText,
  Clock,
  Loader2,
  BookOpen,
  Users
} from 'lucide-react'

interface StudentStats {
  currentClass: string
  currentSection: string
  rollNumber: string
  attendanceRate: number
  averageMarks: number
  totalSubjects: number
  upcomingExams: number
  totalMarksRecords: number
  totalAttendanceRecords: number
  recentMarks: Array<{
    id: string
    subject: string
    examName: string
    obtainedMarks: number
    maxMarks: number
    percentage: number
    date: string
  }>
  upcomingExamsList: Array<{
    id: string
    name: string
    subject: string
    date: string
    maxMarks: number
  }>
}

export default function StudentDashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<StudentStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/student/dashboard/stats')
        if (!response.ok) {
          throw new Error('Failed to fetch dashboard statistics')
        }
        const data = await response.json()
        setStats(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        console.error('Error fetching student dashboard stats:', err)
      } finally {
        setLoading(false)
      }
    }

    if (session?.user?.role === 'STUDENT') {
      fetchStats()
    }
  }, [session])



  const quickActions = [
    {
      title: 'View Marks',
      description: 'Check your latest grades',
      icon: Award,
      href: '/student/marks',
      color: 'bg-blue-500'
    },
    {
      title: 'Attendance History',
      description: 'View your attendance record',
      icon: Calendar,
      href: '/student/attendance',
      color: 'bg-green-500'
    },
    {
      title: 'Download Report',
      description: 'Get your report card',
      icon: FileText,
      href: '/student/reports',
      color: 'bg-purple-500'
    },
    {
      title: 'View Schedule',
      description: 'Check your timetable',
      icon: Clock,
      href: '/student/schedule',
      color: 'bg-orange-500'
    }
  ]

  return (
    <DashboardLayout title="Student Dashboard" navigation={studentNavigation}>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Welcome back, {session?.user?.firstName || 'Student'}!
          </h2>
          <p className="text-gray-600">
            Here&apos;s your academic overview and progress summary.
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading dashboard statistics...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">Error loading dashboard: {error}</p>
          </div>
        )}

        {/* Personal Info Card */}
        {stats && (
          <div className="bg-white rounded-lg border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Class:</span>
                  <span className="font-medium">{stats.currentClass}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Section:</span>
                  <span className="font-medium">{stats.currentSection}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Roll Number:</span>
                  <span className="font-medium">{stats.rollNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Subjects:</span>
                  <span className="font-medium">{stats.totalSubjects}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Attendance Rate:</span>
                  <span className="font-medium text-green-600">{stats.attendanceRate}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Average Marks:</span>
                  <span className="font-medium text-blue-600">{stats.averageMarks}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Upcoming Exams:</span>
                  <span className="font-medium">{stats.upcomingExams}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Records:</span>
                  <span className="font-medium">{stats.totalMarksRecords} marks, {stats.totalAttendanceRecords} attendance</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Attendance</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.attendanceRate}%</div>
                <p className="text-xs text-muted-foreground">
                  Last 30 days
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Marks</CardTitle>
                <Award className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats.averageMarks}%</div>
                <p className="text-xs text-muted-foreground">
                  {stats.totalMarksRecords} exams taken
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Subjects</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">{stats.totalSubjects}</div>
                <p className="text-xs text-muted-foreground">
                  Current semester
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Upcoming Exams</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{stats.upcomingExams}</div>
                <p className="text-xs text-muted-foreground">
                  Scheduled exams
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Recent Marks */}
        {stats && stats.recentMarks.length > 0 && (
          <div className="bg-white rounded-lg border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Marks</h3>
            <div className="space-y-3">
              {stats.recentMarks.slice(0, 5).map((mark) => (
                <div key={mark.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">{mark.subject}</p>
                      <p className="text-sm text-gray-600">{mark.examName}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Score</p>
                      <p className="font-medium text-blue-600">{mark.obtainedMarks}/{mark.maxMarks}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Percentage</p>
                      <p className="font-medium text-green-600">{mark.percentage}%</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Date</p>
                      <p className="font-medium text-purple-600">{new Date(mark.date).toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Card key={action.title} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">{action.title}</h4>
                      <p className="text-xs text-gray-500">{action.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Marks uploaded</p>
                <p className="text-xs text-gray-500">Mathematics - Unit Test 1: 92%</p>
              </div>
              <span className="text-xs text-gray-500">2 days ago</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Attendance marked</p>
                <p className="text-xs text-gray-500">Present in all classes today</p>
              </div>
              <span className="text-xs text-gray-500">1 day ago</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Report card generated</p>
                <p className="text-xs text-gray-500">Term 1 report available for download</p>
              </div>
              <span className="text-xs text-gray-500">1 week ago</span>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
