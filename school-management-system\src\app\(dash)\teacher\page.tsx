'use client'

import { useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { teacherNavigation } from '@/lib/navigation'
import {
  Users,
  ClipboardList,
  Award,
  Clock,
  TrendingUp,
  FileText,
  Bell,
  BookOpen,
  Calendar,
  Loader2
} from 'lucide-react'

interface TeacherStats {
  totalClasses: number
  totalStudents: number
  averageAttendance: number
  averageMarks: number
  upcomingExams: number
  totalMarksGraded: number
  assignedClasses: Array<{
    id: string
    name: string
    subject: string
    students: number
    attendance: number
    nextExam: {
      name: string
      date: string
    } | null
  }>
}

export default function TeacherDashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<TeacherStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/teacher/dashboard/stats')
        if (!response.ok) {
          throw new Error('Failed to fetch dashboard statistics')
        }
        const data = await response.json()
        setStats(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        console.error('Error fetching teacher dashboard stats:', err)
      } finally {
        setLoading(false)
      }
    }

    if (session?.user?.role === 'TEACHER') {
      fetchStats()
    }
  }, [session])



  const quickActions = [
    {
      title: 'Take Attendance',
      description: 'Mark today\'s attendance',
      icon: ClipboardList,
      href: '/teacher/attendance',
      color: 'bg-green-500'
    },
    {
      title: 'Enter Marks',
      description: 'Upload exam results',
      icon: Award,
      href: '/teacher/marks',
      color: 'bg-blue-500'
    },
    {
      title: 'Generate Report',
      description: 'Create class reports',
      icon: FileText,
      href: '/teacher/reports',
      color: 'bg-purple-500'
    },
    {
      title: 'View Schedule',
      description: 'Check your timetable',
      icon: Clock,
      href: '/teacher/schedule',
      color: 'bg-orange-500'
    }
  ]

  return (
    <DashboardLayout title="Teacher Dashboard" navigation={teacherNavigation}>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Welcome back, {session?.user?.firstName || 'Teacher'}!
          </h2>
          <p className="text-gray-600">
            Here&apos;s your teaching overview and quick actions for today.
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading dashboard statistics...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">Error loading dashboard: {error}</p>
          </div>
        )}

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Classes</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalClasses}</div>
                <p className="text-xs text-muted-foreground">
                  Assigned classes
                </p>
              </CardContent>
            </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalStudents}</div>
              <p className="text-xs text-muted-foreground">
                Across all classes
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Attendance</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageAttendance}%</div>
              <p className="text-xs text-muted-foreground">
                Last 30 days
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Marks</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageMarks}%</div>
              <p className="text-xs text-muted-foreground">
                {stats.totalMarksGraded} marks graded
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Upcoming Exams</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.upcomingExams}</div>
              <p className="text-xs text-muted-foreground">
                Scheduled exams
              </p>
            </CardContent>
          </Card>
          </div>
        )}

        {/* Assigned Classes */}
        {stats && (
          <div className="bg-white rounded-lg border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">My Classes</h3>
            {stats.assignedClasses.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {stats.assignedClasses.map((classInfo) => (
                  <Card key={classInfo.id} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader>
                      <CardTitle className="text-lg">{classInfo.name}</CardTitle>
                      <CardDescription>{classInfo.subject}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Students:</span>
                          <span className="font-medium">{classInfo.students}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Attendance:</span>
                          <span className="font-medium text-green-600">{classInfo.attendance}%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Next Exam:</span>
                          <span className="font-medium">
                            {classInfo.nextExam
                              ? `${classInfo.nextExam.name} - ${new Date(classInfo.nextExam.date).toLocaleDateString()}`
                              : 'No upcoming exams'
                            }
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <p className="text-gray-600">No classes assigned yet.</p>
            )}
          </div>
        )}

        {/* Quick Actions */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Card key={action.title} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">{action.title}</h4>
                      <p className="text-xs text-gray-500">{action.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Today's Schedule */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Today&apos;s Schedule</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="font-medium">Grade 8A - Mathematics</p>
                  <p className="text-sm text-gray-600">Room 101</p>
                </div>
              </div>
              <span className="text-sm font-medium">9:00 AM - 10:00 AM</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div>
                  <p className="font-medium">Grade 9A - Physics</p>
                  <p className="text-sm text-gray-600">Lab 2</p>
                </div>
              </div>
              <span className="text-sm font-medium">2:00 PM - 3:00 PM</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                <div>
                  <p className="font-medium">Grade 8B - Mathematics</p>
                  <p className="text-sm text-gray-600">Room 102</p>
                </div>
              </div>
              <span className="text-sm font-medium">4:00 PM - 5:00 PM</span>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
