'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { teacherNavigation } from '@/lib/navigation'
import { getGradeBadgeColor } from '@/lib/grading'
import {
  Calendar,
  FileText,
  Download,
  BarChart3,
  Users,
  Award,
  TrendingUp,
  Printer,
  Eye,
  Loader2,
  AlertCircle,
  Plus
} from 'lucide-react'

interface ReportCard {
  id: string
  studentId: string
  termId: string
  jsonSnapshot: any
  pdfPath?: string
  generatedAt: string
  student: {
    admissionNo: string
    rollNumber: string
    user: {
      firstName: string
      lastName: string
    }
    currentClass: {
      name: string
    }
    currentSection: {
      name: string
    }
  }
  term: {
    name: string
    academicYear: string
  }
}

interface Term {
  id: string
  name: string
  academicYear: string
}

interface Subject {
  id: string
  name: string
  class: {
    name: string
  }
}

export default function TeacherReportsPage() {
  const { data: session } = useSession()
  const [reportCards, setReportCards] = useState<ReportCard[]>([])
  const [terms, setTerms] = useState<Term[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [selectedTerm, setSelectedTerm] = useState('all')
  const [selectedSubject, setSelectedSubject] = useState('all')
  const [loading, setLoading] = useState(true)
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchReportCards()
    fetchTermsAndSubjects()
  }, [selectedTerm, selectedSubject])

  const fetchReportCards = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (selectedTerm !== 'all') params.append('termId', selectedTerm)
      if (selectedSubject !== 'all') params.append('subjectId', selectedSubject)

      const response = await fetch(`/api/teacher/reports?${params}`)
      if (response.ok) {
        const data = await response.json()
        setReportCards(data)
      } else {
        setError('Failed to fetch report cards')
      }
    } catch (error) {
      console.error('Error fetching report cards:', error)
      setError('Error fetching report cards')
    } finally {
      setLoading(false)
    }
  }

  const fetchTermsAndSubjects = async () => {
    try {
      const [termsResponse, subjectsResponse] = await Promise.all([
        fetch('/api/teacher/terms'),
        fetch('/api/teacher/subjects')
      ])

      if (termsResponse.ok) {
        const termsData = await termsResponse.json()
        setTerms(termsData)
      }

      if (subjectsResponse.ok) {
        const subjectsData = await subjectsResponse.json()
        setSubjects(subjectsData)
      }
    } catch (error) {
      console.error('Error fetching terms and subjects:', error)
    }
  }

  const handleDownloadPDF = async (reportCardId: string, studentName: string) => {
    try {
      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'report-card',
          reportCardId: reportCardId
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `report-card-${studentName.replace(/\s+/g, '-')}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        setError('Failed to download PDF')
      }
    } catch (error) {
      console.error('Error downloading PDF:', error)
      setError('Error downloading PDF')
    }
  }

  // Calculate statistics
  const stats = {
    total: reportCards.length,
    generated: reportCards.filter(r => r.pdfPath).length,
    pending: reportCards.filter(r => !r.pdfPath).length,
    averagePercentage: reportCards.length > 0
      ? Math.round(reportCards.reduce((sum, r) => sum + ((r.jsonSnapshot as any)?.summary?.percentage || 0), 0) / reportCards.length)
      : 0
  }

  const getStatusColor = (hasPdf: boolean) => {
    return hasPdf
      ? 'bg-green-100 text-green-800'
      : 'bg-yellow-100 text-yellow-800'
  }

  const getStatusText = (hasPdf: boolean) => {
    return hasPdf ? 'Generated' : 'Pending'
  }

  return (
    <DashboardLayout title="Reports" navigation={teacherNavigation}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Student Reports</h1>
            <p className="text-sm sm:text-base text-gray-600">View and download student report cards</p>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Generated PDFs</CardTitle>
              <Award className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.generated}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending PDFs</CardTitle>
              <Award className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stats.pending}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Class Average</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.averagePercentage}%</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="term">Term</Label>
                <select
                  id="term"
                  value={selectedTerm}
                  onChange={(e) => setSelectedTerm(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Terms</option>
                  {terms.filter(term => term?.id).map(term => (
                    <option key={term.id} value={term.id}>{term.name} ({term.academicYear})</option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="subject">Subject</Label>
                <select
                  id="subject"
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Subjects</option>
                  {subjects.filter(subject => subject?.id).map(subject => (
                    <option key={subject.id} value={subject.id}>
                      {subject.name} ({subject.class.name})
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Report Cards Table */}
        <Card>
          <CardHeader>
            <CardTitle>Student Report Cards</CardTitle>
            <CardDescription>
              Report cards for students in your classes
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <Loader2 className="h-8 w-8 animate-spin mx-auto" />
                <p className="mt-2 text-gray-600">Loading report cards...</p>
              </div>
            ) : reportCards.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No report cards found</p>
                <p className="text-sm text-gray-500 mt-2">Report cards will appear here once generated by admin</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Student
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Class
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Term
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Percentage
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Grade
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {reportCards.filter(card => card?.id).map((card) => {
                      const summary = (card.jsonSnapshot as any)?.summary || {}
                      const studentName = `${card.student.user.firstName} ${card.student.user.lastName}`

                      return (
                        <tr key={card.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                  <span className="text-sm font-medium text-gray-700">
                                    {card.student.user.firstName[0]}{card.student.user.lastName[0]}
                                  </span>
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{studentName}</div>
                                <div className="text-sm text-gray-500">{card.student.admissionNo}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {card.student.currentClass.name} - {card.student.currentSection.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {card.term.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {summary.percentage ? `${summary.percentage.toFixed(1)}%` : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeBadgeColor(summary.grade || 'F')}`}>
                              {summary.grade || 'N/A'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(!!card.pdfPath)}`}>
                              {getStatusText(!!card.pdfPath)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDownloadPDF(card.id, studentName)}
                              >
                                <Download className="w-4 h-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
