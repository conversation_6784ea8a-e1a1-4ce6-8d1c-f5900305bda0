import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { calculateGrade } from '@/lib/grading'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'current-term'
    const classId = searchParams.get('classId')

    // Get date range based on period
    const now = new Date()
    let startDate: Date
    let endDate: Date = now

    switch (period) {
      case 'current-term':
        // Get current term dates
        const currentTerm = await prisma.term.findFirst({
          where: {
            startDate: { lte: now },
            endDate: { gte: now }
          }
        })
        startDate = currentTerm?.startDate || new Date(now.getFullYear(), 0, 1)
        endDate = currentTerm?.endDate || now
        break
      case 'current-year':
        startDate = new Date(now.getFullYear(), 0, 1)
        endDate = new Date(now.getFullYear(), 11, 31)
        break
      case 'last-term':
        const lastTerm = await prisma.term.findFirst({
          where: {
            endDate: { lt: now }
          },
          orderBy: { endDate: 'desc' }
        })
        startDate = lastTerm?.startDate || new Date(now.getFullYear() - 1, 0, 1)
        endDate = lastTerm?.endDate || new Date(now.getFullYear() - 1, 11, 31)
        break
      default:
        startDate = new Date(2020, 0, 1) // All time
        endDate = now
    }

    // Build class filter
    const classFilter = classId && classId !== 'all' ? { currentClassId: classId } : {}

    // 1. Overview Statistics
    const [totalStudents, totalTeachers, totalClasses, totalSubjects] = await Promise.all([
      prisma.student.count({ where: classFilter }),
      prisma.teacher.count(),
      prisma.class.count(),
      prisma.subject.count()
    ])

    // 2. Average Attendance
    const attendanceRecords = await prisma.attendance.findMany({
      where: {
        date: { gte: startDate, lte: endDate },
        ...(classId && classId !== 'all' ? {
          student: { currentClassId: classId }
        } : {})
      }
    })

    const totalAttendanceRecords = attendanceRecords.length
    const presentRecords = attendanceRecords.filter(a => a.status === 'PRESENT').length
    const averageAttendance = totalAttendanceRecords > 0 ? (presentRecords / totalAttendanceRecords) * 100 : 0

    // 3. Average Performance
    const marks = await prisma.mark.findMany({
      where: {
        exam: {
          date: { gte: startDate, lte: endDate }
        },
        ...(classId && classId !== 'all' ? {
          student: { currentClassId: classId }
        } : {})
      },
      include: {
        exam: true,
        student: {
          include: {
            currentClass: true
          }
        }
      }
    })

    const totalMarksRecords = marks.length
    const averagePerformance = totalMarksRecords > 0 
      ? marks.reduce((sum, mark) => sum + (mark.obtainedMarks / mark.exam.maxMarks) * 100, 0) / totalMarksRecords
      : 0

    // 4. Performance by Class
    const classes = await prisma.class.findMany({
      include: {
        students: {
          include: {
            marks: {
              where: {
                exam: {
                  date: { gte: startDate, lte: endDate }
                }
              },
              include: {
                exam: true
              }
            }
          }
        }
      }
    })

    const performanceByClass = classes.map(cls => {
      const classMarks = cls.students.flatMap(s => s.marks)
      const totalStudents = cls.students.length
      
      if (classMarks.length === 0) {
        return {
          className: cls.name,
          averagePercentage: 0,
          totalStudents,
          passRate: 0
        }
      }

      const averagePercentage = classMarks.reduce((sum, mark) => 
        sum + (mark.obtainedMarks / mark.exam.maxMarks) * 100, 0
      ) / classMarks.length

      const passCount = classMarks.filter(mark => 
        (mark.obtainedMarks / mark.exam.maxMarks) * 100 >= 40
      ).length
      const passRate = (passCount / classMarks.length) * 100

      return {
        className: cls.name,
        averagePercentage: Math.round(averagePercentage * 100) / 100,
        totalStudents,
        passRate: Math.round(passRate * 100) / 100
      }
    })

    // 5. Attendance Trends (last 30 days)
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - i)
      return date
    }).reverse()

    const attendanceTrends = await Promise.all(
      last30Days.map(async (date) => {
        const dayAttendance = await prisma.attendance.findMany({
          where: {
            date: {
              gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
              lt: new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1)
            },
            ...(classId && classId !== 'all' ? {
              student: { currentClassId: classId }
            } : {})
          }
        })

        const totalRecords = dayAttendance.length
        const presentRecords = dayAttendance.filter(a => a.status === 'PRESENT').length
        const attendancePercentage = totalRecords > 0 ? (presentRecords / totalRecords) * 100 : 0

        return {
          date: date.toISOString().split('T')[0],
          attendance: Math.round(attendancePercentage * 100) / 100,
          totalStudents: totalRecords
        }
      })
    )

    // 6. Subject Performance
    const subjects = await prisma.subject.findMany({
      include: {
        exams: {
          where: {
            date: { gte: startDate, lte: endDate }
          },
          include: {
            marks: {
              ...(classId && classId !== 'all' ? {
                where: {
                  student: { currentClassId: classId }
                }
              } : {})
            }
          }
        }
      }
    })

    const subjectPerformance = subjects.map(subject => {
      const allMarks = subject.exams.flatMap(exam => exam.marks)
      const totalExams = subject.exams.length
      
      if (allMarks.length === 0) {
        return {
          subject: subject.name,
          averageMarks: 0,
          totalExams,
          passRate: 0
        }
      }

      const averageMarks = allMarks.reduce((sum, mark) => {
        const exam = subject.exams.find(e => e.id === mark.examId)
        return sum + (exam ? (mark.obtainedMarks / exam.maxMarks) * 100 : 0)
      }, 0) / allMarks.length

      const passCount = allMarks.filter(mark => {
        const exam = subject.exams.find(e => e.id === mark.examId)
        return exam ? (mark.obtainedMarks / exam.maxMarks) * 100 >= 40 : false
      }).length
      const passRate = (passCount / allMarks.length) * 100

      return {
        subject: subject.name,
        averageMarks: Math.round(averageMarks * 100) / 100,
        totalExams,
        passRate: Math.round(passRate * 100) / 100
      }
    })

    // 7. Grade Distribution
    const allPercentages = marks.map(mark => (mark.obtainedMarks / mark.exam.maxMarks) * 100)
    const gradeDistribution = [
      { grade: 'A+', count: 0, percentage: 0 },
      { grade: 'A', count: 0, percentage: 0 },
      { grade: 'B+', count: 0, percentage: 0 },
      { grade: 'B', count: 0, percentage: 0 },
      { grade: 'C+', count: 0, percentage: 0 },
      { grade: 'C', count: 0, percentage: 0 },
      { grade: 'D', count: 0, percentage: 0 },
      { grade: 'F', count: 0, percentage: 0 }
    ]

    allPercentages.forEach(percentage => {
      const grade = calculateGrade(percentage)
      const gradeIndex = gradeDistribution.findIndex(g => g.grade === grade)
      if (gradeIndex !== -1) {
        gradeDistribution[gradeIndex].count++
      }
    })

    // Calculate percentages
    const totalGrades = allPercentages.length
    gradeDistribution.forEach(grade => {
      grade.percentage = totalGrades > 0 ? (grade.count / totalGrades) * 100 : 0
    })

    // 8. Monthly Trends (last 12 months)
    const monthlyTrends = await Promise.all(
      Array.from({ length: 12 }, (_, i) => {
        const date = new Date()
        date.setMonth(date.getMonth() - i)
        return date
      }).reverse().map(async (date) => {
        const monthStart = new Date(date.getFullYear(), date.getMonth(), 1)
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0)

        // Get marks for the month
        const monthMarks = await prisma.mark.findMany({
          where: {
            exam: {
              date: { gte: monthStart, lte: monthEnd }
            },
            ...(classId && classId !== 'all' ? {
              student: { currentClassId: classId }
            } : {})
          },
          include: { exam: true }
        })

        // Get attendance for the month
        const monthAttendance = await prisma.attendance.findMany({
          where: {
            date: { gte: monthStart, lte: monthEnd },
            ...(classId && classId !== 'all' ? {
              student: { currentClassId: classId }
            } : {})
          }
        })

        const performance = monthMarks.length > 0 
          ? monthMarks.reduce((sum, mark) => sum + (mark.obtainedMarks / mark.exam.maxMarks) * 100, 0) / monthMarks.length
          : 0

        const attendance = monthAttendance.length > 0
          ? (monthAttendance.filter(a => a.status === 'PRESENT').length / monthAttendance.length) * 100
          : 0

        return {
          month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          performance: Math.round(performance * 100) / 100,
          attendance: Math.round(attendance * 100) / 100
        }
      })
    )

    const analyticsData = {
      overview: {
        totalStudents,
        totalTeachers,
        totalClasses,
        totalSubjects,
        averageAttendance: Math.round(averageAttendance * 100) / 100,
        averagePerformance: Math.round(averagePerformance * 100) / 100
      },
      performanceByClass,
      attendanceTrends,
      subjectPerformance,
      gradeDistribution,
      monthlyTrends
    }

    return NextResponse.json(analyticsData)
  } catch (error) {
    console.error('Error fetching analytics:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
