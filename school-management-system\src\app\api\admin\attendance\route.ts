import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    // const session = await getServerSession(authOptions)
    
    // if (!session || session.user.role !== 'ADMIN') {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const { searchParams } = new URL(request.url)
    const date = searchParams.get('date')
    const classId = searchParams.get('classId')
    const sectionId = searchParams.get('sectionId')

    const where: Record<string, unknown> = {}
    
    if (date) {
      where.date = new Date(date)
    }
    if (classId && classId !== 'all') {
      where.classId = classId
    }
    if (sectionId && sectionId !== 'all') {
      where.sectionId = sectionId
    }

    const attendance = await prisma.attendance.findMany({
      where,
      include: {
        student: {
          include: {
            user: true
          }
        },
        class: true,
        section: true,
        subject: true,
        takenByTeacher: {
          include: {
            user: true
          }
        }
      },
      orderBy: { date: 'desc' }
    })

    return NextResponse.json(attendance)
  } catch (error) {
    console.error('Error fetching attendance:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // const session = await getServerSession(authOptions)
    
    // if (!session || session.user.role !== 'ADMIN') {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const body = await request.json()
    const { studentId, date, status, remarks, classId, sectionId, subjectId, takenByTeacherId } = body

    const attendance = await prisma.attendance.create({
      data: {
        studentId,
        date: new Date(date),
        status,
        remarks,
        classId,
        sectionId,
        subjectId,
        takenByTeacherId
      }
    })

    return NextResponse.json(attendance)
  } catch (error) {
    console.error('Error creating attendance:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
