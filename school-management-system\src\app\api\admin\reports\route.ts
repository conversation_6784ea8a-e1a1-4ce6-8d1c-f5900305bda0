import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { calculateGrade } from '@/lib/grading'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const termId = searchParams.get('termId')
    const classId = searchParams.get('classId')
    const status = searchParams.get('status')

    const where: any = {}
    
    if (termId && termId !== 'all') {
      where.termId = termId
    }
    if (classId && classId !== 'all') {
      where.student = {
        currentClassId: classId
      }
    }
    if (status && status !== 'all') {
      // Note: status is not a field in ReportCard model, this would need to be implemented
      // For now, we'll return all report cards
    }

    const reportCards = await prisma.reportCard.findMany({
      where,
      include: {
        student: {
          include: {
            user: true,
            currentClass: true,
            currentSection: true
          }
        },
        term: true
      },
      orderBy: { generatedAt: 'desc' }
    })

    return NextResponse.json(reportCards)
  } catch (error) {
    console.error('Error fetching report cards:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { termId, classId } = body

    // Get all students in the specified class
    const students = await prisma.student.findMany({
      where: {
        currentClassId: classId
      },
      include: {
        user: true,
        currentClass: true,
        currentSection: true
      }
    })

    const reportCards = []

    for (const student of students) {
      // Get all marks for this student in the specified term
      const marks = await prisma.mark.findMany({
        where: {
          studentId: student.id,
          exam: {
            termId: termId
          }
        },
        include: {
          exam: {
            include: {
              subject: true
            }
          }
        }
      })

      // Calculate total marks and percentage
      let totalObtainedMarks = 0
      let totalMaxMarks = 0

      marks.forEach(mark => {
        totalObtainedMarks += mark.obtainedMarks
        totalMaxMarks += mark.exam.maxMarks
      })

      const percentage = totalMaxMarks > 0 ? (totalObtainedMarks / totalMaxMarks) * 100 : 0

      // Create report card data
      const reportData = {
        studentId: student.id,
        termId: termId,
        jsonSnapshot: {
          student: {
            name: `${student.user.firstName} ${student.user.lastName}`,
            admissionNo: student.admissionNo,
            class: student.currentClass?.name,
            section: student.currentSection?.name
          },
          marks: marks.map(mark => ({
            subject: mark.exam.subject.name,
            exam: mark.exam.name,
            obtainedMarks: mark.obtainedMarks,
            maxMarks: mark.exam.maxMarks,
            percentage: (mark.obtainedMarks / mark.exam.maxMarks) * 100
          })),
          totalMarks: totalMaxMarks,
          obtainedMarks: totalObtainedMarks,
          percentage: percentage,
          grade: calculateGrade(percentage)
        }
      }

      // Check if report card already exists
      const existingReport = await prisma.reportCard.findUnique({
        where: {
          studentId_termId: {
            studentId: student.id,
            termId: termId
          }
        }
      })

      if (existingReport) {
        // Update existing report card
        const updatedReport = await prisma.reportCard.update({
          where: {
            id: existingReport.id
          },
          data: {
            jsonSnapshot: reportData.jsonSnapshot
          }
        })
        reportCards.push(updatedReport)
      } else {
        // Create new report card
        const newReport = await prisma.reportCard.create({
          data: reportData
        })
        reportCards.push(newReport)
      }
    }

    return NextResponse.json(reportCards)
  } catch (error) {
    console.error('Error generating report cards:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}


