import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma as db } from '@/lib/db';
import { hasPermission } from '@/lib/rbac';
import bcrypt from 'bcryptjs';
import * as XLSX from 'xlsx';

// GET /api/admin/students/bulk - Export students to CSV
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(session.user.role, 'students:read')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const classId = searchParams.get('classId') || '';

    // Build where clause
    const where: Record<string, unknown> = {};
    if (classId) {
      where.currentClassId = classId;
    }

    // Fetch all students with related data
    const students = await db.student.findMany({
      where,
      include: {
        user: true,
        currentClass: true,
        currentSection: true,
      },
      orderBy: [
        { user: { lastName: 'asc' } },
        { user: { firstName: 'asc' } },
      ],
    });

    // Convert to CSV format
    const csvHeaders = [
      'ID',
      'First Name',
      'Last Name',
      'Email',
      'Date of Birth',
      'Gender',
      'Phone Number',
      'Address',
      'Emergency Contact',
      'Emergency Phone',
      'Admission Date',
      'Class',
      'Section',
      'Parent Name',
      'Parent Phone',
      'Parent Email',
    ];

    const csvRows = students.map(student => [
      student.id,
      student.user?.firstName || '',
      student.user?.lastName || '',
      student.user?.email || '',
      student.dob.toISOString().split('T')[0],
      student.gender,
      student.user?.phone || '',
      student.address || '',
      student.guardianName || '',
      student.guardianPhone || '',
      student.createdAt.toISOString().split('T')[0],
      student.currentClass?.name || '',
      student.currentSection?.name || '',
      student.guardianName || '',
      student.guardianPhone || '',
      '', // No parent email in schema
    ]);

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    // Return CSV file
    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="students-${new Date().toISOString().split('T')[0]}.csv"`,
      },
    });
  } catch (error) {
    console.error('Error exporting students:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/students/bulk - Bulk import students from CSV
export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(session.user.role, 'students:write')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    const isExcel = file.name.endsWith('.xlsx') || file.name.endsWith('.xls');
    const isCsv = file.name.endsWith('.csv');

    if (!isExcel && !isCsv) {
      return NextResponse.json(
        { error: 'Only CSV and Excel files (.csv, .xlsx, .xls) are allowed' },
        { status: 400 }
      );
    }

    let data: unknown[][] = [];

    if (isExcel) {
      // Read Excel file
      const buffer = await file.arrayBuffer();
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    } else {
      // Read CSV file
      const text = await file.text();
      const lines = text.split('\n').filter(line => line.trim());
      data = lines.map(line => line.split(',').map(cell => cell.trim().replace(/"/g, '')));
    }

    if (data.length < 2) {
      return NextResponse.json(
        { error: 'File must have at least a header row and one data row' },
        { status: 400 }
      );
    }

    // Parse headers
    const headers = data[0].map((h: unknown) => String(h).trim());

    // Expected headers (case-insensitive)
    const expectedHeaders = [
      'first name', 'last name', 'email', 'date of birth', 'gender',
      'phone number', 'address', 'guardian name', 'guardian phone',
      'class', 'section', 'roll number', 'academic year', 'admission number'
    ];

    const missingHeaders = expectedHeaders.filter(expected => 
      !headers.some(header => header.toLowerCase() === expected.toLowerCase())
    );

    if (missingHeaders.length > 0) {
      return NextResponse.json(
        { 
          error: 'Missing required headers',
          missingHeaders 
        },
        { status: 400 }
      );
    }

    // Process data rows
    const results = {
      success: 0,
      errors: [] as string[],
      total: data.length - 1,
    };

    // Get all classes for lookup
    const classes = await db.class.findMany({
      include: {
        sections: true,
      },
    });

    // Process each row
    for (let i = 1; i < data.length; i++) {
      try {
        const values = data[i].map((v: unknown) => String(v || '').trim());
        const rowData: Record<string, string> = {};

        headers.forEach((header, index) => {
          rowData[header.toLowerCase()] = values[index] || '';
        });

        // Validate required fields
        if (!rowData['first name'] || !rowData['last name'] || !rowData['email']) {
          results.errors.push(`Row ${i + 1}: Missing required fields (first name, last name, or email)`);
          continue;
        }

        if (!rowData['date of birth']) {
          results.errors.push(`Row ${i + 1}: Date of birth is required`);
          continue;
        }

        if (!rowData['gender']) {
          results.errors.push(`Row ${i + 1}: Gender is required`);
          continue;
        }

        if (!rowData['guardian name']) {
          results.errors.push(`Row ${i + 1}: Guardian name is required`);
          continue;
        }

        if (!rowData['guardian phone']) {
          results.errors.push(`Row ${i + 1}: Guardian phone is required`);
          continue;
        }

        if (!rowData['class'] || !rowData['section']) {
          results.errors.push(`Row ${i + 1}: Class and section are required`);
          continue;
        }

        if (!rowData['academic year']) {
          results.errors.push(`Row ${i + 1}: Academic year is required`);
          continue;
        }

        // Check if email already exists
        const existingUser = await db.user.findUnique({
          where: { email: rowData['email'] },
        });

        if (existingUser) {
          results.errors.push(`Row ${i + 1}: Email ${rowData['email']} already exists`);
          continue;
        }

        // Find class by name and section
        let classId = null;
        let sectionId = null;
        const classRecord = classes.find(c =>
          c.name.toLowerCase() === rowData['class'].toLowerCase()
        );

        if (classRecord) {
          const sectionRecord = classRecord.sections.find(s =>
            s.name.toLowerCase() === rowData['section'].toLowerCase()
          );

          if (sectionRecord) {
            classId = classRecord.id;
            sectionId = sectionRecord.id;
          }
        }

        if (!classId) {
          results.errors.push(`Row ${i + 1}: Class "${rowData['class']}" Section "${rowData['section']}" not found`);
          continue;
        }

        // Validate gender
        const validGenders = ['MALE', 'FEMALE', 'OTHER'];
        const gender = rowData['gender']?.toUpperCase();
        if (!validGenders.includes(gender)) {
          results.errors.push(`Row ${i + 1}: Invalid gender "${rowData['gender']}". Must be MALE, FEMALE, or OTHER`);
          continue;
        }

        // Validate date of birth
        const dateOfBirth = new Date(rowData['date of birth']);
        if (isNaN(dateOfBirth.getTime())) {
          results.errors.push(`Row ${i + 1}: Invalid date of birth "${rowData['date of birth']}". Use YYYY-MM-DD format`);
          continue;
        }

        // Validate academic year format (YYYY-YYYY)
        const academicYear = rowData['academic year'];
        const academicYearPattern = /^\d{4}-\d{4}$/;
        if (!academicYearPattern.test(academicYear)) {
          results.errors.push(`Row ${i + 1}: Invalid academic year format "${academicYear}". Use YYYY-YYYY format (e.g., 2024-2025)`);
          continue;
        }

        // Generate a secure default password
        const defaultPassword = 'Student@12345';
        const hashedPassword = await bcrypt.hash(defaultPassword, 12);

        // Create user account
        const user = await db.user.create({
          data: {
            email: rowData['email'],
            hashedPassword: hashedPassword,
            role: 'STUDENT',
            firstName: rowData['first name'],
            lastName: rowData['last name'],
            phone: rowData['phone number'] || null,
          },
        });

        // Generate admission number if not provided
        const admissionNo = rowData['admission number'] || `STU${Date.now()}-${i}`;

        // Create student record
        const student = await db.student.create({
          data: {
            userId: user.id,
            admissionNo: admissionNo,
            dob: dateOfBirth,
            gender: gender as 'MALE' | 'FEMALE' | 'OTHER',
            address: rowData['address'] || null,
            guardianName: rowData['guardian name'],
            guardianPhone: rowData['guardian phone'],
            currentClassId: classId,
            currentSectionId: sectionId,
            rollNumber: rowData['roll number'] || null,
          },
        });

        // Create enrollment record
        await db.enrollment.create({
          data: {
            studentId: student.id,
            classId: classId!,
            sectionId: sectionId!,
            academicYear: academicYear,
            active: true,
          },
        });

        results.success++;
      } catch (error) {
        results.errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      message: 'Bulk import completed',
      results,
    });
  } catch (error) {
    console.error('Error importing students:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
