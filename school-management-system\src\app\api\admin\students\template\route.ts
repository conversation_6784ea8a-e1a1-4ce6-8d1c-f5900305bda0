import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/rbac';
import * as fs from 'fs';
import * as path from 'path';

// GET /api/admin/students/template - Download student import template
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(session.user.role, 'students:read')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get the file type from query params (default to xlsx)
    const { searchParams } = new URL(request.url);
    const fileType = searchParams.get('type') || 'xlsx';

    let filePath: string;
    let contentType: string;
    let fileName: string;

    if (fileType === 'csv') {
      filePath = path.join(process.cwd(), 'templates', 'student-import-template.csv');
      contentType = 'text/csv';
      fileName = 'student-import-template.csv';
    } else {
      filePath = path.join(process.cwd(), 'templates', 'student-import-template.xlsx');
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      fileName = 'student-import-template.xlsx';
    }

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { error: 'Template file not found' },
        { status: 404 }
      );
    }

    // Read the file
    const fileBuffer = fs.readFileSync(filePath);

    // Return the file
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': fileBuffer.length.toString(),
      },
    });
  } catch (error) {
    console.error('Error downloading template:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
