import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all terms
    const terms = await prisma.term.findMany({
      orderBy: { name: 'asc' }
    })

    return NextResponse.json(terms)
  } catch (error) {
    console.error('Error fetching admin terms:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
