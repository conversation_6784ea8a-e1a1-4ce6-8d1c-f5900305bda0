import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { 
  sendMarksNotification, 
  sendReportCardNotification, 
  sendAttendanceAlert,
  sendBulkMarksNotifications,
  isEmailServiceConfigured 
} from '@/lib/email-service'
import { calculateGrade } from '@/lib/grading'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !['ADMIN', 'TEACHER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    if (!isEmailServiceConfigured()) {
      return NextResponse.json({ error: 'Email service not configured' }, { status: 503 })
    }

    const body = await request.json()
    const { type, data } = body

    switch (type) {
      case 'marks-published': {
        const { markId } = data
        
        // Get mark details with student and exam info
        const mark = await prisma.mark.findUnique({
          where: { id: markId },
          include: {
            student: {
              include: {
                user: true,
                currentClass: true,
                currentSection: true
              }
            },
            exam: {
              include: {
                subject: true
              }
            }
          }
        })

        if (!mark) {
          return NextResponse.json({ error: 'Mark not found' }, { status: 404 })
        }

        const percentage = (mark.obtainedMarks / mark.exam.maxMarks) * 100
        const grade = calculateGrade(percentage)

        const success = await sendMarksNotification({
          studentEmail: mark.student.user.email,
          studentName: `${mark.student.user.firstName} ${mark.student.user.lastName}`,
          examName: mark.exam.name,
          subjectName: mark.exam.subject.name,
          obtainedMarks: mark.obtainedMarks,
          maxMarks: mark.exam.maxMarks,
          percentage: percentage,
          grade: grade,
          remarks: mark.remarks,
          portalUrl: `${process.env.NEXTAUTH_URL}/student/marks`
        })

        return NextResponse.json({ success })
      }

      case 'bulk-marks-published': {
        const { examId } = data
        
        // Get all marks for this exam
        const marks = await prisma.mark.findMany({
          where: { examId },
          include: {
            student: {
              include: {
                user: true,
                currentClass: true,
                currentSection: true
              }
            },
            exam: {
              include: {
                subject: true
              }
            }
          }
        })

        const notifications = marks.map(mark => {
          const percentage = (mark.obtainedMarks / mark.exam.maxMarks) * 100
          const grade = calculateGrade(percentage)

          return {
            studentEmail: mark.student.user.email,
            studentName: `${mark.student.user.firstName} ${mark.student.user.lastName}`,
            examName: mark.exam.name,
            subjectName: mark.exam.subject.name,
            obtainedMarks: mark.obtainedMarks,
            maxMarks: mark.exam.maxMarks,
            percentage: percentage,
            grade: grade,
            remarks: mark.remarks,
            portalUrl: `${process.env.NEXTAUTH_URL}/student/marks`
          }
        })

        const result = await sendBulkMarksNotifications(notifications)
        return NextResponse.json(result)
      }

      case 'report-card-generated': {
        const { reportCardId } = data
        
        // Get report card details
        const reportCard = await prisma.reportCard.findUnique({
          where: { id: reportCardId },
          include: {
            student: {
              include: {
                user: true,
                currentClass: true,
                currentSection: true
              }
            },
            term: true
          }
        })

        if (!reportCard) {
          return NextResponse.json({ error: 'Report card not found' }, { status: 404 })
        }

        const summary = (reportCard.jsonSnapshot as any)?.summary || {}

        const success = await sendReportCardNotification({
          studentEmail: reportCard.student.user.email,
          studentName: `${reportCard.student.user.firstName} ${reportCard.student.user.lastName}`,
          termName: reportCard.term.name,
          academicYear: reportCard.term.academicYear,
          percentage: summary.percentage || 0,
          grade: summary.grade || 'F',
          rank: summary.rank || 0,
          totalStudents: summary.totalStudents || 0,
          portalUrl: `${process.env.NEXTAUTH_URL}/student/reports`
        })

        return NextResponse.json({ success })
      }

      case 'attendance-alert': {
        const { studentId, attendanceData } = data
        
        // Get student details
        const student = await prisma.student.findUnique({
          where: { id: studentId },
          include: {
            user: true,
            currentClass: true,
            currentSection: true
          }
        })

        if (!student) {
          return NextResponse.json({ error: 'Student not found' }, { status: 404 })
        }

        // Use parent email if available, otherwise student email
        const parentEmail = student.parentEmail || student.user.email

        const success = await sendAttendanceAlert({
          parentEmail,
          studentName: `${student.user.firstName} ${student.user.lastName}`,
          admissionNo: student.admissionNo,
          className: student.currentClass?.name || '',
          sectionName: student.currentSection?.name || '',
          attendancePercentage: attendanceData.percentage,
          presentDays: attendanceData.presentDays,
          totalDays: attendanceData.totalDays,
          portalUrl: `${process.env.NEXTAUTH_URL}/student/attendance`
        })

        return NextResponse.json({ success })
      }

      default:
        return NextResponse.json({ error: 'Invalid notification type' }, { status: 400 })
    }
  } catch (error) {
    console.error('Error sending notification:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Test email endpoint
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const testEmail = searchParams.get('email')

    if (!testEmail) {
      return NextResponse.json({ error: 'Email parameter required' }, { status: 400 })
    }

    if (!isEmailServiceConfigured()) {
      return NextResponse.json({ 
        error: 'Email service not configured',
        message: 'Please configure SMTP settings in environment variables'
      }, { status: 503 })
    }

    const { sendTestEmail } = await import('@/lib/email-service')
    const success = await sendTestEmail(testEmail)

    return NextResponse.json({ 
      success,
      message: success ? 'Test email sent successfully' : 'Failed to send test email'
    })
  } catch (error) {
    console.error('Error sending test email:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
