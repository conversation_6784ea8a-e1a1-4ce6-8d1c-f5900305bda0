import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { 
  generateReportCardPDF, 
  generateClassReportHTML, 
  generatePDFFromHTML,
  ensureReportsDirectory, 
  generateReportFilename,
  type ReportCardData 
} from '@/lib/pdf-generator'
import { calculateGrade } from '@/lib/grading'
import path from 'path'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !['ADMIN', 'TEACHER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { type, reportCardId, termId, classId, studentId } = body

    if (type === 'report-card') {
      // Generate individual report card PDF
      let reportCard

      if (reportCardId) {
        // Generate from existing report card
        reportCard = await prisma.reportCard.findUnique({
          where: { id: reportCardId },
          include: {
            student: {
              include: {
                user: true,
                currentClass: true,
                currentSection: true
              }
            },
            term: true
          }
        })
      } else if (studentId && termId) {
        // Generate new report card
        reportCard = await generateReportCardData(studentId, termId)
      } else {
        return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
      }

      if (!reportCard) {
        return NextResponse.json({ error: 'Report card not found' }, { status: 404 })
      }

      // Prepare data for PDF generation
      const reportData: ReportCardData = {
        student: {
          name: `${reportCard.student.user.firstName} ${reportCard.student.user.lastName}`,
          admissionNo: reportCard.student.admissionNo,
          rollNumber: reportCard.student.rollNumber,
          class: reportCard.student.currentClass?.name || '',
          section: reportCard.student.currentSection?.name || '',
          academicYear: reportCard.term.academicYear,
          dateOfBirth: reportCard.student.dateOfBirth.toISOString(),
          fatherName: reportCard.student.fatherName,
          motherName: reportCard.student.motherName
        },
        term: {
          name: reportCard.term.name,
          startDate: reportCard.term.startDate.toISOString(),
          endDate: reportCard.term.endDate.toISOString(),
          academicYear: reportCard.term.academicYear
        },
        marks: (reportCard.jsonSnapshot as any).marks || [],
        summary: (reportCard.jsonSnapshot as any).summary || {
          totalMarks: 0,
          obtainedMarks: 0,
          percentage: 0,
          grade: 'F',
          rank: 0,
          totalStudents: 0
        },
        attendance: (reportCard.jsonSnapshot as any).attendance || {
          totalDays: 0,
          presentDays: 0,
          absentDays: 0,
          percentage: 0
        },
        remarks: (reportCard.jsonSnapshot as any).remarks,
        generatedAt: new Date().toISOString()
      }

      // Generate PDF
      const reportsDir = ensureReportsDirectory()
      const filename = generateReportFilename('report-card', reportCard.student.admissionNo, reportCard.termId)
      const filePath = path.join(reportsDir, filename)
      
      const { buffer } = await generateReportCardPDF(reportData, filePath)

      // Update report card with PDF path
      await prisma.reportCard.update({
        where: { id: reportCard.id },
        data: { pdfPath: `/reports/${filename}` }
      })

      return new NextResponse(buffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      })

    } else if (type === 'class-report') {
      // Generate class performance report
      if (!termId || !classId) {
        return NextResponse.json({ error: 'Missing termId or classId' }, { status: 400 })
      }

      const classData = await generateClassReportData(termId, classId)
      if (!classData) {
        return NextResponse.json({ error: 'Class data not found' }, { status: 404 })
      }

      const htmlContent = generateClassReportHTML(classData)
      const pdfBuffer = await generatePDFFromHTML(htmlContent)

      const filename = generateReportFilename('class-report', `${classData.class.name}-${classData.class.section}`, termId)

      return new NextResponse(pdfBuffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      })

    } else {
      return NextResponse.json({ error: 'Invalid report type' }, { status: 400 })
    }

  } catch (error) {
    console.error('Error generating PDF:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

async function generateReportCardData(studentId: string, termId: string) {
  // Get student with all related data
  const student = await prisma.student.findUnique({
    where: { id: studentId },
    include: {
      user: true,
      currentClass: true,
      currentSection: true,
      marks: {
        where: {
          exam: {
            termId: termId
          }
        },
        include: {
          exam: {
            include: {
              subject: true
            }
          }
        }
      },
      attendance: {
        where: {
          date: {
            gte: new Date(new Date().getFullYear(), 0, 1), // Start of current year
            lte: new Date()
          }
        }
      }
    }
  })

  if (!student) return null

  const term = await prisma.term.findUnique({
    where: { id: termId }
  })

  if (!term) return null

  // Calculate totals and grades
  const totalMaxMarks = student.marks.reduce((sum, mark) => sum + mark.exam.maxMarks, 0)
  const totalObtainedMarks = student.marks.reduce((sum, mark) => sum + mark.obtainedMarks, 0)
  const percentage = totalMaxMarks > 0 ? (totalObtainedMarks / totalMaxMarks) * 100 : 0

  // Calculate attendance
  const totalAttendanceDays = student.attendance.length
  const presentDays = student.attendance.filter(a => a.status === 'PRESENT').length
  const attendancePercentage = totalAttendanceDays > 0 ? (presentDays / totalAttendanceDays) * 100 : 0

  // Get class ranking
  const classStudents = await prisma.student.findMany({
    where: { currentClassId: student.currentClassId },
    include: {
      marks: {
        where: {
          exam: {
            termId: termId
          }
        },
        include: {
          exam: true
        }
      }
    }
  })

  const studentsWithPercentages = classStudents.map(s => {
    const maxMarks = s.marks.reduce((sum, mark) => sum + mark.exam.maxMarks, 0)
    const obtainedMarks = s.marks.reduce((sum, mark) => sum + mark.obtainedMarks, 0)
    const perc = maxMarks > 0 ? (obtainedMarks / maxMarks) * 100 : 0
    return { id: s.id, percentage: perc }
  }).sort((a, b) => b.percentage - a.percentage)

  const rank = studentsWithPercentages.findIndex(s => s.id === studentId) + 1

  // Create or update report card
  const reportData = {
    studentId,
    termId,
    jsonSnapshot: {
      student: {
        name: `${student.user.firstName} ${student.user.lastName}`,
        admissionNo: student.admissionNo,
        class: student.currentClass?.name,
        section: student.currentSection?.name
      },
      marks: student.marks.map(mark => ({
        subject: mark.exam.subject.name,
        exam: mark.exam.name,
        obtainedMarks: mark.obtainedMarks,
        maxMarks: mark.exam.maxMarks,
        percentage: (mark.obtainedMarks / mark.exam.maxMarks) * 100,
        grade: calculateGrade((mark.obtainedMarks / mark.exam.maxMarks) * 100)
      })),
      summary: {
        totalMarks: totalMaxMarks,
        obtainedMarks: totalObtainedMarks,
        percentage: percentage,
        grade: calculateGrade(percentage),
        rank: rank,
        totalStudents: classStudents.length
      },
      attendance: {
        totalDays: totalAttendanceDays,
        presentDays: presentDays,
        absentDays: totalAttendanceDays - presentDays,
        percentage: attendancePercentage
      }
    }
  }

  // Check if report card exists
  const existingReport = await prisma.reportCard.findUnique({
    where: {
      studentId_termId: {
        studentId,
        termId
      }
    },
    include: {
      student: {
        include: {
          user: true,
          currentClass: true,
          currentSection: true
        }
      },
      term: true
    }
  })

  if (existingReport) {
    return await prisma.reportCard.update({
      where: { id: existingReport.id },
      data: { jsonSnapshot: reportData.jsonSnapshot },
      include: {
        student: {
          include: {
            user: true,
            currentClass: true,
            currentSection: true
          }
        },
        term: true
      }
    })
  } else {
    return await prisma.reportCard.create({
      data: reportData,
      include: {
        student: {
          include: {
            user: true,
            currentClass: true,
            currentSection: true
          }
        },
        term: true
      }
    })
  }
}

async function generateClassReportData(termId: string, classId: string) {
  const classInfo = await prisma.class.findUnique({
    where: { id: classId },
    include: {
      students: {
        include: {
          user: true,
          marks: {
            where: {
              exam: {
                termId: termId
              }
            },
            include: {
              exam: true
            }
          },
          attendance: {
            where: {
              date: {
                gte: new Date(new Date().getFullYear(), 0, 1),
                lte: new Date()
              }
            }
          }
        }
      }
    }
  })

  if (!classInfo) return null

  const term = await prisma.term.findUnique({
    where: { id: termId }
  })

  if (!term) return null

  // Calculate student performances
  const studentsWithPerformance = classInfo.students.map(student => {
    const totalMaxMarks = student.marks.reduce((sum, mark) => sum + mark.exam.maxMarks, 0)
    const totalObtainedMarks = student.marks.reduce((sum, mark) => sum + mark.obtainedMarks, 0)
    const percentage = totalMaxMarks > 0 ? (totalObtainedMarks / totalMaxMarks) * 100 : 0

    const totalAttendanceDays = student.attendance.length
    const presentDays = student.attendance.filter(a => a.status === 'PRESENT').length
    const attendancePercentage = totalAttendanceDays > 0 ? (presentDays / totalAttendanceDays) * 100 : 0

    return {
      name: `${student.user.firstName} ${student.user.lastName}`,
      admissionNo: student.admissionNo,
      rollNumber: student.rollNumber,
      totalMarks: totalMaxMarks,
      obtainedMarks: totalObtainedMarks,
      percentage: percentage,
      grade: calculateGrade(percentage),
      rank: 0, // Will be calculated after sorting
      attendancePercentage: attendancePercentage
    }
  }).sort((a, b) => b.percentage - a.percentage)

  // Assign ranks
  studentsWithPerformance.forEach((student, index) => {
    student.rank = index + 1
  })

  // Calculate summary statistics
  const totalStudents = studentsWithPerformance.length
  const averagePercentage = totalStudents > 0 
    ? studentsWithPerformance.reduce((sum, s) => sum + s.percentage, 0) / totalStudents 
    : 0
  const passedStudents = studentsWithPerformance.filter(s => s.percentage >= 40).length
  const failedStudents = totalStudents - passedStudents
  const highestPercentage = totalStudents > 0 ? Math.max(...studentsWithPerformance.map(s => s.percentage)) : 0
  const lowestPercentage = totalStudents > 0 ? Math.min(...studentsWithPerformance.map(s => s.percentage)) : 0

  return {
    class: {
      name: classInfo.name,
      section: 'A', // Default section, could be enhanced
      academicYear: term.academicYear
    },
    term: {
      name: term.name,
      startDate: term.startDate.toISOString(),
      endDate: term.endDate.toISOString()
    },
    students: studentsWithPerformance,
    summary: {
      totalStudents,
      averagePercentage,
      passedStudents,
      failedStudents,
      highestPercentage,
      lowestPercentage
    },
    generatedAt: new Date().toISOString()
  }
}
