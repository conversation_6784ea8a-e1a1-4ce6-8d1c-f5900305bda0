import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'STUDENT') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get student record for the logged-in user
    const student = await prisma.student.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Get all terms that have exams for this student's class
    const terms = await prisma.term.findMany({
      where: {
        exams: {
          some: {
            subject: {
              classId: student.currentClassId
            }
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    return NextResponse.json(terms)
  } catch (error) {
    console.error('Error fetching student terms:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
