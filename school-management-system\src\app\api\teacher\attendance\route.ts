import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { hasPermission } from '@/lib/rbac';
import { z } from 'zod';

// Validation schema for attendance data
const AttendanceSchema = z.object({
  classId: z.number().min(1, 'Class ID is required'),
  date: z.string().min(1, 'Date is required'),
  attendanceRecords: z.array(z.object({
    studentId: z.number().min(1, 'Student ID is required'),
    status: z.enum(['PRESENT', 'ABSENT', 'LATE', 'HALF_DAY']),
    remarks: z.string().optional(),
  })),
});

// GET /api/teacher/attendance - Get attendance records for teacher's classes
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !hasPermission(session.user.role, 'attendance:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const classId = searchParams.get('classId');
    const date = searchParams.get('date');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: Record<string, unknown> = {};
    
    if (session.user.role === 'TEACHER') {
      // Teachers can only see attendance for their assigned classes
      where.class = {
        teacherId: session.user.teacherId,
      };
    }

    if (classId) {
      where.classId = parseInt(classId);
    }

    if (date) {
      where.date = new Date(date);
    }

    // Get attendance records with pagination
    const [attendanceRecords, total] = await Promise.all([
      prisma.attendance.findMany({
        where,
        skip,
        take: limit,
        orderBy: { date: 'desc' },
        include: {
          student: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              rollNumber: true,
            },
          },
          class: {
            select: {
              id: true,
              name: true,
              section: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      }),
      prisma.attendance.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      attendanceRecords,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching attendance:', error);
    return NextResponse.json(
      { error: 'Failed to fetch attendance' },
      { status: 500 }
    );
  }
}

// POST /api/teacher/attendance - Mark attendance
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !hasPermission(session.user.role, 'attendance:write')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = AttendanceSchema.parse(body);

    // Check if class exists and teacher has permission
    const classData = await prisma.class.findUnique({
      where: { id: validatedData.classId },
      include: {
        teacher: true,
        students: true,
      },
    });

    if (!classData) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      );
    }

    // For teachers, check if they are assigned to this class
    if (session.user.role === 'TEACHER' && classData.teacherId !== session.user.teacherId) {
      return NextResponse.json(
        { error: 'You are not authorized to mark attendance for this class' },
        { status: 403 }
      );
    }

    // Check if attendance already exists for this date and class
    const existingAttendance = await prisma.attendance.findFirst({
      where: {
        classId: validatedData.classId,
        date: new Date(validatedData.date),
      },
    });

    if (existingAttendance) {
      return NextResponse.json(
        { error: 'Attendance for this date has already been marked' },
        { status: 400 }
      );
    }

    // Validate that all students belong to the class
    const classStudentIds = classData.students.map(s => s.id);
    const attendanceStudentIds = validatedData.attendanceRecords.map(r => r.studentId);
    
    const invalidStudents = attendanceStudentIds.filter(id => !classStudentIds.includes(id));
    if (invalidStudents.length > 0) {
      return NextResponse.json(
        { error: 'Some students do not belong to this class' },
        { status: 400 }
      );
    }

    // Create attendance records
    const attendanceRecords = await prisma.$transaction(
      validatedData.attendanceRecords.map(record => 
        prisma.attendance.create({
          data: {
            studentId: record.studentId,
            classId: validatedData.classId,
            date: new Date(validatedData.date),
            status: record.status,
            remarks: record.remarks,
          },
          include: {
            student: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                rollNumber: true,
              },
            },
          },
        })
      )
    );

    return NextResponse.json(
      { 
        message: 'Attendance marked successfully',
        attendanceRecords,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    console.error('Error marking attendance:', error);
    return NextResponse.json(
      { error: 'Failed to mark attendance' },
      { status: 500 }
    );
  }
}

// PUT /api/teacher/attendance - Update attendance
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !hasPermission(session.user.role, 'UPDATE_ATTENDANCE')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { classId, date, attendanceRecords } = body;

    if (!classId || !date || !attendanceRecords) {
      return NextResponse.json(
        { error: 'Class ID, date, and attendance records are required' },
        { status: 400 }
      );
    }

    // Check if class exists and teacher has permission
    const classData = await prisma.class.findUnique({
      where: { id: parseInt(classId) },
      include: {
        teacher: true,
      },
    });

    if (!classData) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      );
    }

    // For teachers, check if they are assigned to this class
    if (session.user.role === 'TEACHER' && classData.teacherId !== session.user.teacherId) {
      return NextResponse.json(
        { error: 'You are not authorized to update attendance for this class' },
        { status: 403 }
      );
    }

    // Update attendance records
    const updatedRecords = await prisma.$transaction(
      attendanceRecords.map((record: { studentId: string; status: string }) =>
        prisma.attendance.updateMany({
          where: {
            studentId: record.studentId,
            classId: parseInt(classId),
            date: new Date(date),
          },
          data: {
            status: record.status,
            remarks: record.remarks,
          },
        })
      )
    );

    return NextResponse.json({
      message: 'Attendance updated successfully',
      updatedRecords,
    });
  } catch (error) {
    console.error('Error updating attendance:', error);
    return NextResponse.json(
      { error: 'Failed to update attendance' },
      { status: 500 }
    );
  }
}
