import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'TEACHER') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get teacher record for the logged-in user
    const teacher = await prisma.teacher.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!teacher) {
      return NextResponse.json({ error: 'Teacher not found' }, { status: 404 })
    }

    const { searchParams } = new URL(request.url)
    const termId = searchParams.get('termId')
    const subjectId = searchParams.get('subjectId')

    // Get all report cards for students in classes where this teacher teaches
    const where: any = {
      student: {
        currentClass: {
          subjects: {
            some: {
              teacherId: teacher.id
            }
          }
        }
      }
    }
    
    if (termId && termId !== 'all') {
      where.termId = termId
    }

    if (subjectId && subjectId !== 'all') {
      // Filter by subject - only show reports for students in classes where teacher teaches this subject
      where.student.currentClass.subjects = {
        some: {
          teacherId: teacher.id,
          id: subjectId
        }
      }
    }

    const reportCards = await prisma.reportCard.findMany({
      where,
      include: {
        student: {
          include: {
            user: true,
            currentClass: true,
            currentSection: true
          }
        },
        term: true
      },
      orderBy: { generatedAt: 'desc' }
    })

    return NextResponse.json(reportCards)
  } catch (error) {
    console.error('Error fetching teacher report cards:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
