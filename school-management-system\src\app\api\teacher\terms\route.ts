import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'TEACHER') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get teacher record for the logged-in user
    const teacher = await prisma.teacher.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!teacher) {
      return NextResponse.json({ error: 'Teacher not found' }, { status: 404 })
    }

    // Get all terms that have exams
    const terms = await prisma.term.findMany({
      where: {
        exams: {
          some: {}
        }
      },
      orderBy: { name: 'asc' }
    })

    return NextResponse.json(terms)
  } catch (error) {
    console.error('Error fetching teacher terms:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
