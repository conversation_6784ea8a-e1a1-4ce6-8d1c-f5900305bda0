import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { 
  uploadProfilePicture, 
  uploadDocument, 
  uploadBulkFile,
  UPLOAD_CONFIG 
} from '@/lib/file-upload'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string
    const category = formData.get('category') as string

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!type) {
      return NextResponse.json({ error: 'Upload type is required' }, { status: 400 })
    }

    let result

    switch (type) {
      case 'profile-picture': {
        // Only allow users to upload their own profile pictures or admins to upload any
        if (session.user.role !== 'ADMIN') {
          const targetUserId = formData.get('userId') as string
          if (targetUserId && targetUserId !== session.user.id) {
            return NextResponse.json({ error: 'Cannot upload profile picture for another user' }, { status: 403 })
          }
        }

        const userId = (formData.get('userId') as string) || session.user.id
        result = await uploadProfilePicture(file, userId)

        if (result.success) {
          // Update user profile picture in database
          await prisma.user.update({
            where: { id: userId },
            data: { profilePicture: result.filePath }
          })
        }
        break
      }

      case 'document': {
        if (!category) {
          return NextResponse.json({ error: 'Document category is required' }, { status: 400 })
        }

        result = await uploadDocument(file, category as any, session.user.id)
        break
      }

      case 'bulk-import': {
        // Only admins can upload bulk files
        if (session.user.role !== 'ADMIN') {
          return NextResponse.json({ error: 'Only administrators can upload bulk files' }, { status: 403 })
        }

        if (!category) {
          return NextResponse.json({ error: 'Bulk import category is required' }, { status: 400 })
        }

        result = await uploadBulkFile(file, category as any)
        break
      }

      default:
        return NextResponse.json({ error: 'Invalid upload type' }, { status: 400 })
    }

    if (result.success) {
      return NextResponse.json({
        success: true,
        filePath: result.filePath,
        fileName: result.fileName,
        fileSize: result.fileSize
      })
    } else {
      return NextResponse.json({
        success: false,
        error: result.error
      }, { status: 400 })
    }

  } catch (error) {
    console.error('Error uploading file:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Return upload configuration for the frontend
    return NextResponse.json({
      maxFileSize: UPLOAD_CONFIG.maxFileSize,
      allowedImageTypes: UPLOAD_CONFIG.allowedImageTypes,
      allowedDocumentTypes: UPLOAD_CONFIG.allowedDocumentTypes,
      uploadPaths: UPLOAD_CONFIG.uploadPaths
    })
  } catch (error) {
    console.error('Error getting upload config:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
