import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { MarksEntryForm } from '../marks/marks-entry-form'

// Mock the validation module
vi.mock('@/lib/marks-validation', () => ({
  validateMarkEntry: vi.fn(),
  formatValidationErrors: vi.fn()
}))

const mockExam = {
  id: 'exam1',
  name: 'Mid Term Exam',
  maxMarks: 100,
  date: new Date('2024-12-15'),
  subject: {
    id: 'subject1',
    name: 'Mathematics'
  }
}

const mockStudents = [
  {
    id: 'student1',
    admissionNo: 'STU001',
    rollNumber: '001',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    className: 'Grade 8',
    sectionName: 'A',
    currentMark: null,
    hasMarks: false
  },
  {
    id: 'student2',
    admissionNo: 'STU002',
    rollNumber: '002',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    className: 'Grade 8',
    sectionName: 'A',
    currentMark: {
      id: 'mark1',
      obtainedMarks: 85,
      remarks: 'Good work'
    },
    hasMarks: true
  }
]

describe('MarksEntryForm', () => {
  const mockOnSave = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockOnSave.mockResolvedValue(undefined)
  })

  it('should render exam information', () => {
    render(
      <MarksEntryForm
        exam={mockExam}
        students={mockStudents}
        onSave={mockOnSave}
        saving={false}
      />
    )

    expect(screen.getByText('Mid Term Exam')).toBeInTheDocument()
    expect(screen.getByText('Mathematics')).toBeInTheDocument()
    expect(screen.getByText('Max Marks: 100')).toBeInTheDocument()
  })

  it('should render student list', () => {
    render(
      <MarksEntryForm
        exam={mockExam}
        students={mockStudents}
        onSave={mockOnSave}
        saving={false}
      />
    )

    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    expect(screen.getByText('STU001')).toBeInTheDocument()
    expect(screen.getByText('STU002')).toBeInTheDocument()
  })

  it('should show existing marks for students who have them', () => {
    render(
      <MarksEntryForm
        exam={mockExam}
        students={mockStudents}
        onSave={mockOnSave}
        saving={false}
      />
    )

    // Jane Smith should have existing marks
    const janeMarksInput = screen.getByDisplayValue('85')
    expect(janeMarksInput).toBeInTheDocument()
    
    const janeRemarksInput = screen.getByDisplayValue('Good work')
    expect(janeRemarksInput).toBeInTheDocument()
  })

  it('should handle marks input changes', async () => {
    render(
      <MarksEntryForm
        exam={mockExam}
        students={mockStudents}
        onSave={mockOnSave}
        saving={false}
      />
    )

    // Find John's marks input (should be empty initially)
    const marksInputs = screen.getAllByPlaceholderText('Enter marks')
    const johnMarksInput = marksInputs[0] // First student without existing marks

    fireEvent.change(johnMarksInput, { target: { value: '90' } })
    
    expect(johnMarksInput).toHaveValue('90')
  })

  it('should handle remarks input changes', async () => {
    render(
      <MarksEntryForm
        exam={mockExam}
        students={mockStudents}
        onSave={mockOnSave}
        saving={false}
      />
    )

    const remarksInputs = screen.getAllByPlaceholderText('Optional remarks')
    const johnRemarksInput = remarksInputs[0]

    fireEvent.change(johnRemarksInput, { target: { value: 'Excellent work' } })
    
    expect(johnRemarksInput).toHaveValue('Excellent work')
  })

  it('should call onSave with correct data', async () => {
    const { validateMarkEntry } = await import('@/lib/marks-validation')
    vi.mocked(validateMarkEntry).mockReturnValue({ isValid: true, errors: [] })

    render(
      <MarksEntryForm
        exam={mockExam}
        students={mockStudents}
        onSave={mockOnSave}
        saving={false}
      />
    )

    // Enter marks for John
    const marksInputs = screen.getAllByPlaceholderText('Enter marks')
    const johnMarksInput = marksInputs[0]
    
    fireEvent.change(johnMarksInput, { target: { value: '90' } })

    // Click save
    const saveButton = screen.getByText('Save All Marks')
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith([
        {
          studentId: 'student1',
          obtainedMarks: 90,
          remarks: undefined
        }
      ])
    })
  })

  it('should show validation errors', async () => {
    const { validateMarkEntry } = await import('@/lib/marks-validation')
    vi.mocked(validateMarkEntry).mockReturnValue({
      isValid: false,
      errors: [{ field: 'obtainedMarks', message: 'Marks cannot exceed maximum marks' }]
    })

    render(
      <MarksEntryForm
        exam={mockExam}
        students={mockStudents}
        onSave={mockOnSave}
        saving={false}
      />
    )

    // Enter invalid marks
    const marksInputs = screen.getAllByPlaceholderText('Enter marks')
    const johnMarksInput = marksInputs[0]
    
    fireEvent.change(johnMarksInput, { target: { value: '105' } })

    // Click save
    const saveButton = screen.getByText('Save All Marks')
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(screen.getByText('Please fix the validation errors before saving.')).toBeInTheDocument()
    })
  })

  it('should disable save button when saving', () => {
    render(
      <MarksEntryForm
        exam={mockExam}
        students={mockStudents}
        onSave={mockOnSave}
        saving={true}
      />
    )

    const saveButton = screen.getByText('Saving...')
    expect(saveButton).toBeDisabled()
  })

  it('should show empty state when no students', () => {
    render(
      <MarksEntryForm
        exam={mockExam}
        students={[]}
        onSave={mockOnSave}
        saving={false}
      />
    )

    expect(screen.getByText('No students found for this exam')).toBeInTheDocument()
  })

  it('should show graded status for students with marks', () => {
    render(
      <MarksEntryForm
        exam={mockExam}
        students={mockStudents}
        onSave={mockOnSave}
        saving={false}
      />
    )

    // Jane should show as graded
    expect(screen.getByText('Graded')).toBeInTheDocument()
  })

  it('should calculate and display percentage', async () => {
    render(
      <MarksEntryForm
        exam={mockExam}
        students={mockStudents}
        onSave={mockOnSave}
        saving={false}
      />
    )

    // Enter marks for John
    const marksInputs = screen.getAllByPlaceholderText('Enter marks')
    const johnMarksInput = marksInputs[0]
    
    fireEvent.change(johnMarksInput, { target: { value: '80' } })

    // Should show percentage (80/100 = 80%)
    await waitFor(() => {
      expect(screen.getByText('80.0%')).toBeInTheDocument()
    })
  })

  it('should handle decimal marks correctly', async () => {
    render(
      <MarksEntryForm
        exam={mockExam}
        students={mockStudents}
        onSave={mockOnSave}
        saving={false}
      />
    )

    const marksInputs = screen.getAllByPlaceholderText('Enter marks')
    const johnMarksInput = marksInputs[0]
    
    fireEvent.change(johnMarksInput, { target: { value: '85.5' } })

    await waitFor(() => {
      expect(screen.getByText('85.5%')).toBeInTheDocument()
    })
  })
})
