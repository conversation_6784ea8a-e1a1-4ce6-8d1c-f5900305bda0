'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Calculator,
  TrendingUp,
  Award
} from 'lucide-react'
import { getGradeBadgeColor } from '@/lib/grading'

interface Mark {
  id: string
  obtainedMarks: number
  exam: {
    id: string
    name: string
    maxMarks: number
    weightagePercent: number
    subject: {
      id: string
      name: string
    }
    term: {
      id: string
      name: string
    }
  }
}

interface GradeCalculatorProps {
  marks: Mark[]
  studentName?: string
  className?: string
}

export interface GradeInfo {
  grade: string
  gpa: number
  percentage: number
  totalMarks: number
  maxMarks: number
  subjectWiseGrades: Array<{
    subjectId: string
    subjectName: string
    totalObtained: number
    totalMax: number
    percentage: number
    grade: string
    gpa: number
  }>
}

export const calculateGrade = (percentage: number): string => {
  if (percentage >= 90) return 'A+'
  if (percentage >= 80) return 'A'
  if (percentage >= 70) return 'B+'
  if (percentage >= 60) return 'B'
  if (percentage >= 50) return 'C+'
  if (percentage >= 40) return 'C'
  if (percentage >= 30) return 'D'
  return 'F'
}

export const calculateGPA = (percentage: number): number => {
  if (percentage >= 90) return 4.0
  if (percentage >= 80) return 3.7
  if (percentage >= 70) return 3.3
  if (percentage >= 60) return 3.0
  if (percentage >= 50) return 2.7
  if (percentage >= 40) return 2.3
  if (percentage >= 30) return 2.0
  return 0.0
}



export const calculateOverallGrade = (marks: Mark[]): GradeInfo => {
  if (marks.length === 0) {
    return {
      grade: 'N/A',
      gpa: 0,
      percentage: 0,
      totalMarks: 0,
      maxMarks: 0,
      subjectWiseGrades: []
    }
  }

  // Group marks by subject
  const subjectMarks = marks.reduce((acc, mark) => {
    const subjectId = mark.exam.subject.id
    if (!acc[subjectId]) {
      acc[subjectId] = {
        subjectId,
        subjectName: mark.exam.subject.name,
        marks: []
      }
    }
    acc[subjectId].marks.push(mark)
    return acc
  }, {} as Record<string, { subjectId: string, subjectName: string, marks: Mark[] }>)

  // Calculate subject-wise grades
  const subjectWiseGrades = Object.values(subjectMarks).map(subject => {
    const totalObtained = subject.marks.reduce((sum, mark) => sum + mark.obtainedMarks, 0)
    const totalMax = subject.marks.reduce((sum, mark) => sum + mark.exam.maxMarks, 0)
    const percentage = totalMax > 0 ? Math.round((totalObtained / totalMax) * 100 * 100) / 100 : 0
    const grade = calculateGrade(percentage)
    const gpa = calculateGPA(percentage)

    return {
      subjectId: subject.subjectId,
      subjectName: subject.subjectName,
      totalObtained,
      totalMax,
      percentage,
      grade,
      gpa
    }
  })

  // Calculate overall grade
  const totalObtained = marks.reduce((sum, mark) => sum + mark.obtainedMarks, 0)
  const totalMax = marks.reduce((sum, mark) => sum + mark.exam.maxMarks, 0)
  const overallPercentage = totalMax > 0 ? Math.round((totalObtained / totalMax) * 100 * 100) / 100 : 0
  const overallGrade = calculateGrade(overallPercentage)
  const overallGPA = subjectWiseGrades.length > 0 
    ? Math.round(subjectWiseGrades.reduce((sum, subject) => sum + subject.gpa, 0) / subjectWiseGrades.length * 100) / 100
    : 0

  return {
    grade: overallGrade,
    gpa: overallGPA,
    percentage: overallPercentage,
    totalMarks: totalObtained,
    maxMarks: totalMax,
    subjectWiseGrades
  }
}

export default function GradeCalculator({ marks, studentName, className }: GradeCalculatorProps) {
  const gradeInfo = calculateOverallGrade(marks)

  return (
    <div className="space-y-6">
      {/* Overall Grade Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calculator className="w-5 h-5" />
            <span>Overall Performance</span>
          </CardTitle>
          {studentName && (
            <CardDescription>
              Academic performance for {studentName} {className && `(${className})`}
            </CardDescription>
          )}
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {gradeInfo.totalMarks}/{gradeInfo.maxMarks}
              </div>
              <p className="text-sm text-gray-600">Total Marks</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {gradeInfo.percentage}%
              </div>
              <p className="text-sm text-gray-600">Percentage</p>
            </div>
            <div className="text-center">
              <Badge variant="outline" className={`text-lg px-4 py-2 ${getGradeBadgeColor(gradeInfo.grade)}`}>
                {gradeInfo.grade}
              </Badge>
              <p className="text-sm text-gray-600 mt-2">Grade</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {gradeInfo.gpa}
              </div>
              <p className="text-sm text-gray-600">GPA</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subject-wise Performance */}
      {gradeInfo.subjectWiseGrades.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5" />
              <span>Subject-wise Performance</span>
            </CardTitle>
            <CardDescription>
              Detailed breakdown by subject
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {gradeInfo.subjectWiseGrades.map((subject) => (
                <div key={subject.subjectId} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{subject.subjectName}</h3>
                    <p className="text-sm text-gray-600">
                      {subject.totalObtained}/{subject.totalMax} marks • {subject.percentage}% • GPA: {subject.gpa}
                    </p>
                  </div>
                  <Badge variant="outline" className={getGradeBadgeColor(subject.grade)}>
                    {subject.grade}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Grade Scale Reference */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="w-5 h-5" />
            <span>Grading Scale</span>
          </CardTitle>
          <CardDescription>
            Grade point scale reference
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center p-3 border rounded">
              <Badge className="bg-green-100 text-green-800 border-green-200 mb-2">A+</Badge>
              <p className="font-medium">90-100%</p>
              <p className="text-gray-600">GPA: 4.0</p>
            </div>
            <div className="text-center p-3 border rounded">
              <Badge className="bg-green-100 text-green-800 border-green-200 mb-2">A</Badge>
              <p className="font-medium">80-89%</p>
              <p className="text-gray-600">GPA: 3.7</p>
            </div>
            <div className="text-center p-3 border rounded">
              <Badge className="bg-blue-100 text-blue-800 border-blue-200 mb-2">B+</Badge>
              <p className="font-medium">70-79%</p>
              <p className="text-gray-600">GPA: 3.3</p>
            </div>
            <div className="text-center p-3 border rounded">
              <Badge className="bg-blue-100 text-blue-800 border-blue-200 mb-2">B</Badge>
              <p className="font-medium">60-69%</p>
              <p className="text-gray-600">GPA: 3.0</p>
            </div>
            <div className="text-center p-3 border rounded">
              <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 mb-2">C+</Badge>
              <p className="font-medium">50-59%</p>
              <p className="text-gray-600">GPA: 2.7</p>
            </div>
            <div className="text-center p-3 border rounded">
              <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 mb-2">C</Badge>
              <p className="font-medium">40-49%</p>
              <p className="text-gray-600">GPA: 2.3</p>
            </div>
            <div className="text-center p-3 border rounded">
              <Badge className="bg-orange-100 text-orange-800 border-orange-200 mb-2">D</Badge>
              <p className="font-medium">30-39%</p>
              <p className="text-gray-600">GPA: 2.0</p>
            </div>
            <div className="text-center p-3 border rounded">
              <Badge className="bg-red-100 text-red-800 border-red-200 mb-2">F</Badge>
              <p className="font-medium">0-29%</p>
              <p className="text-gray-600">GPA: 0.0</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
