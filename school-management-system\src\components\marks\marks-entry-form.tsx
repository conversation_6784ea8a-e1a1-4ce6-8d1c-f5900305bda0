'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { validateMarkEntry, formatValidationErrors, ValidationError } from '@/lib/marks-validation'
import {
  Users,
  Save,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react'

interface Student {
  id: string
  admissionNo: string
  rollNumber: string
  firstName: string
  lastName: string
  email: string
  className: string
  sectionName: string
  currentMark: {
    id: string
    obtainedMarks: number
    remarks?: string
  } | null
  hasMarks: boolean
}

interface Exam {
  id: string
  name: string
  maxMarks: number
  date: string
  subject: {
    id: string
    name: string
    code: string
    class: {
      id: string
      name: string
    }
  }
  term: {
    id: string
    name: string
  }
}

interface MarksEntryFormProps {
  exam: Exam
  students: Student[]
  onSave: (marks: Array<{studentId: string, obtainedMarks: number, remarks?: string}>) => Promise<void>
  saving?: boolean
}

export default function MarksEntryForm({ exam, students, onSave, saving = false }: MarksEntryFormProps) {
  const [marks, setMarks] = useState<Record<string, { obtainedMarks: string, remarks: string }>>(() => {
    const initialMarks: Record<string, { obtainedMarks: string, remarks: string }> = {}
    students.forEach((student) => {
      initialMarks[student.id] = {
        obtainedMarks: student.currentMark?.obtainedMarks?.toString() || '',
        remarks: student.currentMark?.remarks || ''
      }
    })
    return initialMarks
  })
  const [errors, setErrors] = useState<Record<string, ValidationError[]>>({})
  const [generalError, setGeneralError] = useState<string>('')

  const handleMarkChange = (studentId: string, field: 'obtainedMarks' | 'remarks', value: string) => {
    setMarks(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        [field]: value
      }
    }))

    // Clear errors when user starts typing
    if (errors[studentId]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[studentId]
        return newErrors
      })
    }

    // Clear general error
    if (generalError) {
      setGeneralError('')
    }
  }

  const validateMarks = () => {
    const newErrors: Record<string, ValidationError[]> = {}
    let hasErrors = false

    Object.entries(marks).forEach(([studentId, mark]) => {
      if (mark.obtainedMarks !== '') {
        const obtainedMarks = parseFloat(mark.obtainedMarks)
        if (!isNaN(obtainedMarks)) {
          const validation = validateMarkEntry(
            studentId,
            exam.id,
            obtainedMarks,
            exam.maxMarks,
            mark.remarks
          )

          if (!validation.isValid) {
            newErrors[studentId] = validation.errors
            hasErrors = true
          }
        } else {
          newErrors[studentId] = [{ field: 'obtainedMarks', message: 'Invalid marks format' }]
          hasErrors = true
        }
      }
    })

    setErrors(newErrors)
    return !hasErrors
  }

  const handleSave = async () => {
    // Clear previous errors
    setGeneralError('')

    if (!validateMarks()) {
      setGeneralError('Please fix the validation errors before saving.')
      return
    }

    // Prepare marks data for submission
    const marksData = Object.entries(marks)
      .filter(([_, mark]) => mark.obtainedMarks !== '')
      .map(([studentId, mark]) => ({
        studentId,
        obtainedMarks: parseFloat(mark.obtainedMarks),
        remarks: mark.remarks || undefined
      }))

    if (marksData.length === 0) {
      setGeneralError('No marks to save. Please enter marks for at least one student.')
      return
    }

    try {
      await onSave(marksData)
    } catch (error) {
      setGeneralError('Failed to save marks. Please try again.')
      console.error('Error saving marks:', error)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="w-5 h-5" />
          <span>Students ({students.length})</span>
        </CardTitle>
        <CardDescription>
          Enter marks for each student. Maximum marks: {exam.maxMarks}
        </CardDescription>

        {/* General Error Message */}
        {generalError && (
          <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
            <AlertCircle className="w-4 h-4 text-red-600" />
            <span className="text-sm text-red-700">{generalError}</span>
          </div>
        )}

        {/* Validation Info */}
        <div className="flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <Info className="w-4 h-4 text-blue-600" />
          <span className="text-sm text-blue-700">
            Marks can be entered with up to 2 decimal places (e.g., 85.5). Leave blank to skip a student.
          </span>
        </div>

        <div className="flex justify-end">
          <Button onClick={handleSave} disabled={saving}>
            <Save className="w-4 h-4 mr-2" />
            {saving ? 'Saving...' : 'Save All Marks'}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {students.length === 0 ? (
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No students found for this exam</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Desktop Table */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Student
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Roll No.
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Section
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Marks (/{exam.maxMarks})
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Remarks
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {students.filter(student => student?.id).map((student) => {
                    const studentErrors = errors[student.id] || []
                    const hasErrors = studentErrors.length > 0

                    return (
                      <tr key={student.id} className={hasErrors ? 'bg-red-50' : ''}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {student.firstName} {student.lastName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {student.admissionNo}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {student.rollNumber || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {student.sectionName || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Input
                            type="number"
                            min="0"
                            max={exam.maxMarks}
                            step="0.01"
                            value={marks[student.id]?.obtainedMarks || ''}
                            onChange={(e) => handleMarkChange(student.id, 'obtainedMarks', e.target.value)}
                            className={`w-20 ${hasErrors ? 'border-red-500' : ''}`}
                            placeholder="0"
                          />
                          {hasErrors && (
                            <div className="text-xs text-red-500 mt-1">
                              {studentErrors.map((error, index) => (
                                <div key={index}>{error.message}</div>
                              ))}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Input
                            type="text"
                            value={marks[student.id]?.remarks || ''}
                            onChange={(e) => handleMarkChange(student.id, 'remarks', e.target.value)}
                            className="w-32"
                            placeholder="Optional"
                            maxLength={500}
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {student.hasMarks ? (
                            <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Graded
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                              Pending
                            </span>
                          )}
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="lg:hidden space-y-4">
              {students.filter(student => student?.id).map((student) => {
                const studentErrors = errors[student.id] || []
                const hasErrors = studentErrors.length > 0

                return (
                  <Card key={student.id} className={`p-4 ${hasErrors ? 'border-red-500 bg-red-50' : ''}`}>
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {student.firstName} {student.lastName}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {student.admissionNo} • Roll: {student.rollNumber || '-'} • Section: {student.sectionName || '-'}
                          </p>
                        </div>
                        <div className="ml-4">
                          {student.hasMarks ? (
                            <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Graded
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                              Pending
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor={`marks-${student.id}`} className="text-sm font-medium text-gray-700">
                            Marks (/{exam.maxMarks})
                          </Label>
                          <Input
                            id={`marks-${student.id}`}
                            type="number"
                            min="0"
                            max={exam.maxMarks}
                            step="0.01"
                            value={marks[student.id]?.obtainedMarks || ''}
                            onChange={(e) => handleMarkChange(student.id, 'obtainedMarks', e.target.value)}
                            className={`mt-1 ${hasErrors ? 'border-red-500' : ''}`}
                            placeholder="0"
                          />
                          {hasErrors && (
                            <div className="text-xs text-red-500 mt-1">
                              {studentErrors.map((error, index) => (
                                <div key={index}>{error.message}</div>
                              ))}
                            </div>
                          )}
                        </div>
                        <div>
                          <Label htmlFor={`remarks-${student.id}`} className="text-sm font-medium text-gray-700">
                            Remarks
                          </Label>
                          <Input
                            id={`remarks-${student.id}`}
                            type="text"
                            value={marks[student.id]?.remarks || ''}
                            onChange={(e) => handleMarkChange(student.id, 'remarks', e.target.value)}
                            className="mt-1"
                            placeholder="Optional"
                            maxLength={500}
                          />
                        </div>
                      </div>
                    </div>
                  </Card>
                )
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
