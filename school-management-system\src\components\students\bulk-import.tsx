'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  Download, 
  FileText, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  ArrowLeft,
  Loader2
} from 'lucide-react';

interface ImportResult {
  success: number;
  errors: string[];
  total: number;
}

export function BulkImport() {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<ImportResult | null>(null);

  const isValidFileType = (fileName: string) => {
    const validExtensions = ['.csv', '.xlsx', '.xls'];
    return validExtensions.some(ext => fileName.toLowerCase().endsWith(ext));
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!isValidFileType(file.name)) {
        setError('Please select a CSV or Excel file (.csv, .xlsx, .xls)');
        return;
      }
      setSelectedFile(file);
      setError(null);
      setResult(null);
    }
  };

  const handleFileDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file) {
      if (!isValidFileType(file.name)) {
        setError('Please select a CSV or Excel file (.csv, .xlsx, .xls)');
        return;
      }
      setSelectedFile(file);
      setError(null);
      setResult(null);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleImport = async () => {
    if (!selectedFile) {
      setError('Please select a file first');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await fetch('/api/admin/students/bulk', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Import failed');
      }

      setResult(data.results);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Import failed');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadTemplate = async (type: 'csv' | 'xlsx' = 'xlsx') => {
    try {
      const response = await fetch(`/api/admin/students/template?type=${type}`);

      if (!response.ok) {
        throw new Error('Failed to download template');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `student-import-template.${type}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading template:', error);
      setError('Failed to download template. Please try again.');
    }
  };

  const handleClear = () => {
    setSelectedFile(null);
    setError(null);
    setResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            onClick={() => router.push('/admin/students')}
            className="p-0 h-auto"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Students
          </Button>
          <div>
            <CardTitle>Bulk Import Students</CardTitle>
            <CardDescription>
              Import multiple students from CSV or Excel files
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <XCircle className="w-4 h-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-2">Instructions:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Download the template below to see the required format</li>
            <li>• Required fields: First Name, Last Name, Email, Date of Birth, Gender, Guardian Name, Guardian Phone, Class, Section, Academic Year</li>
            <li>• Date of Birth should be in YYYY-MM-DD format (e.g., 2010-05-15)</li>
            <li>• Gender should be one of: MALE, FEMALE, OTHER</li>
            <li>• Academic Year should be in YYYY-YYYY format (e.g., 2024-2025)</li>
            <li>• Class and Section must match existing classes in the system</li>
            <li>• Email addresses must be unique</li>
            <li>• All students will be assigned the default password: Student@12345</li>
          </ul>
        </div>

        {/* Template Download */}
        <div className="flex justify-center gap-4">
          <Button
            variant="outline"
            onClick={() => handleDownloadTemplate('xlsx')}
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Download Excel Template
          </Button>
          <Button
            variant="outline"
            onClick={() => handleDownloadTemplate('csv')}
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Download CSV Template
          </Button>
        </div>

        {/* File Upload */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            selectedFile 
              ? 'border-green-300 bg-green-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDrop={handleFileDrop}
          onDragOver={handleDragOver}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".csv,.xlsx,.xls"
            onChange={handleFileSelect}
            className="hidden"
          />

          {!selectedFile ? (
            <div>
              <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <p className="text-lg font-medium text-gray-700 mb-2">
                Drop your file here, or click to browse
              </p>
              <p className="text-sm text-gray-500 mb-4">
                Supports CSV and Excel files (.csv, .xlsx, .xls)
              </p>
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
              >
                Choose File
              </Button>
            </div>
          ) : (
            <div>
              <FileText className="w-12 h-12 mx-auto text-green-600 mb-4" />
              <p className="text-lg font-medium text-green-700 mb-2">
                File Selected: {selectedFile.name}
              </p>
              <p className="text-sm text-green-600 mb-4">
                Size: {(selectedFile.size / 1024).toFixed(1)} KB
              </p>
              <div className="flex gap-2 justify-center">
                <Button
                  onClick={handleImport}
                  disabled={loading}
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="w-4 h-4 mr-2" />
                  )}
                  Import Students
                </Button>
                <Button
                  variant="outline"
                  onClick={handleClear}
                  disabled={loading}
                >
                  Clear
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Import Results */}
        {result && (
          <div className="border rounded-lg p-4">
            <h3 className="font-medium text-lg mb-4">Import Results</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{result.success}</div>
                <div className="text-sm text-gray-600">Successfully Imported</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{result.errors.length}</div>
                <div className="text-sm text-gray-600">Errors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{result.total}</div>
                <div className="text-sm text-gray-600">Total Records</div>
              </div>
            </div>

            {result.success > 0 && (
              <Alert className="mb-4">
                <CheckCircle className="w-4 h-4" />
                <AlertDescription>
                  Successfully imported {result.success} students!
                </AlertDescription>
              </Alert>
            )}

            {result.errors.length > 0 && (
              <div>
                <h4 className="font-medium text-red-700 mb-2">Errors:</h4>
                <div className="max-h-40 overflow-y-auto space-y-1">
                  {result.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-600 flex items-start gap-2">
                      <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>{error}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="mt-4 flex gap-2">
              <Button
                onClick={() => router.push('/admin/students')}
                className="flex-1"
              >
                View Students
              </Button>
              <Button
                variant="outline"
                onClick={handleClear}
              >
                Import Another File
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
