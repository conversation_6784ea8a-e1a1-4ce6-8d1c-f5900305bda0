'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Download, 
  Upload,
  ChevronLeft,
  ChevronRight,
  Loader2
} from 'lucide-react';

interface Student {
  id: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string | null;
  };
  dob: Date | string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  address?: string | null;
  guardianName: string;
  guardianPhone: string;
  currentClass?: {
    id: string;
    name: string;
    createdAt?: Date;
    updatedAt?: Date;
  } | null;
  currentSection?: {
    id: string;
    name: string;
  } | null;
  createdAt: Date | string;
}

interface Class {
  id: string;
  name: string;
  sections: {
    id: string;
    name: string;
  }[];
}

interface StudentTableProps {
  students: Student[];
  classes: Class[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

export function StudentTable({ students, classes, pagination }: StudentTableProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedGender, setSelectedGender] = useState('');

  const handleSearch = () => {
    const params = new URLSearchParams();
    if (searchTerm) params.append('search', searchTerm);
    if (selectedClass) params.append('classId', selectedClass);
    if (selectedGender) params.append('gender', selectedGender);
    params.append('page', '1');
    
    router.push(`/admin/students?${params.toString()}`);
  };

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams();
    if (searchTerm) params.append('search', searchTerm);
    if (selectedClass) params.append('classId', selectedClass);
    if (selectedGender) params.append('gender', selectedGender);
    params.append('page', page.toString());
    
    router.push(`/admin/students?${params.toString()}`);
  };

  const handleDelete = async (studentId: string, studentName: string) => {
    if (!confirm(`Are you sure you want to delete ${studentName}? This action cannot be undone.`)) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/students/${studentId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to delete student');
      }

      // Refresh the page to show updated data
      router.refresh();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete student');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = () => {
    const params = new URLSearchParams();
    if (selectedClass) params.append('classId', selectedClass);
    
    const url = `/api/admin/students/bulk?${params.toString()}`;
    window.open(url, '_blank');
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString();
  };

  const getGenderLabel = (gender: string) => {
    switch (gender) {
      case 'MALE': return 'Male';
      case 'FEMALE': return 'Female';
      case 'OTHER': return 'Other';
      default: return gender;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <CardTitle>Students</CardTitle>
            <CardDescription>
              Manage student information and records
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleExport}
              disabled={loading}
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button
              onClick={() => router.push('/admin/students/bulk')}
              variant="outline"
              disabled={loading}
            >
              <Upload className="w-4 h-4 mr-2" />
              Import
            </Button>
            <Button
              onClick={() => router.push('/admin/students/new')}
              disabled={loading}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Student
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Search and Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Class</label>
            <select
              value={selectedClass}
              onChange={(e) => setSelectedClass(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Classes</option>
              {classes.filter(cls => cls?.id).map((cls) =>
                cls.sections.filter(section => section?.id).map((section) => (
                  <option key={`${cls.id}-${section.id}`} value={`${cls.id}-${section.id}`}>
                    {cls.name} - {section.name}
                  </option>
                ))
              )}
            </select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Gender</label>
            <select
              value={selectedGender}
              onChange={(e) => setSelectedGender(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Genders</option>
              <option value="MALE">Male</option>
              <option value="FEMALE">Female</option>
              <option value="OTHER">Other</option>
            </select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">&nbsp;</label>
            <Button onClick={handleSearch} className="w-full" disabled={loading}>
              {loading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Search className="w-4 h-4 mr-2" />
              )}
              Search
            </Button>
          </div>
        </div>

        {/* Students Table - Desktop */}
        <div className="hidden lg:block overflow-x-auto">
          <table className="w-full border-collapse border border-gray-200">
            <thead>
              <tr className="bg-gray-50">
                <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700">
                  Name
                </th>
                <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700">
                  Email
                </th>
                <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700">
                  Class
                </th>
                <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700">
                  Gender
                </th>
                <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700">
                  Phone
                </th>
                <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700">
                  Admission Date
                </th>
                <th className="border border-gray-200 px-4 py-2 text-center text-sm font-medium text-gray-700">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {students.filter(student => student?.id).map((student) => (
                <tr key={student.id} className="hover:bg-gray-50">
                  <td className="border border-gray-200 px-4 py-2">
                    <div>
                      <div className="font-medium">
                        {student.user.firstName} {student.user.lastName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatDate(student.dob)}
                      </div>
                    </div>
                  </td>
                  <td className="border border-gray-200 px-4 py-2">
                    {student.user.email}
                  </td>
                  <td className="border border-gray-200 px-4 py-2">
                    {student.currentClass ? `${student.currentClass.name} - ${student.currentSection?.name || 'N/A'}` : 'Not assigned'}
                  </td>
                  <td className="border border-gray-200 px-4 py-2">
                    {getGenderLabel(student.gender)}
                  </td>
                  <td className="border border-gray-200 px-4 py-2">
                    {student.user.phone || '-'}
                  </td>
                  <td className="border border-gray-200 px-4 py-2">
                    {formatDate(student.createdAt)}
                  </td>
                  <td className="border border-gray-200 px-4 py-2">
                    <div className="flex justify-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/admin/students/${student.id}`)}
                        disabled={loading}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/admin/students/${student.id}/edit`)}
                        disabled={loading}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(student.id, `${student.user.firstName} ${student.user.lastName}`)}
                        disabled={loading}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Students Cards - Mobile & Tablet */}
        <div className="lg:hidden space-y-4">
          {students.filter(student => student?.id).map((student) => (
            <Card key={student.id} className="p-4">
              <div className="flex flex-col space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
                      {student.user.firstName} {student.user.lastName}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                      {student.user.email}
                    </p>
                  </div>
                  <div className="flex space-x-1 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push(`/admin/students/${student.id}`)}
                      disabled={loading}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push(`/admin/students/${student.id}/edit`)}
                      disabled={loading}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(student.id, `${student.user.firstName} ${student.user.lastName}`)}
                      disabled={loading}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Class:</span>
                    <p className="text-gray-600 dark:text-gray-400">
                      {student.currentClass ? `${student.currentClass.name} - ${student.currentSection?.name || 'N/A'}` : 'Not assigned'}
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Gender:</span>
                    <p className="text-gray-600 dark:text-gray-400">{getGenderLabel(student.gender)}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Phone:</span>
                    <p className="text-gray-600 dark:text-gray-400">{student.user.phone || '-'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">DOB:</span>
                    <p className="text-gray-600 dark:text-gray-400">{formatDate(student.dob)}</p>
                  </div>
                </div>
                
                <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Admitted: {formatDate(student.createdAt)}
                  </span>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0 mt-6">
            <div className="text-sm text-gray-700 text-center sm:text-left">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.totalCount)} of{' '}
              {pagination.totalCount} students
            </div>
            
            <div className="flex justify-center sm:justify-end gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={!pagination.hasPrevPage || loading}
              >
                <ChevronLeft className="w-4 h-4 sm:mr-1" />
                <span className="hidden sm:inline">Previous</span>
              </Button>
              
              <div className="flex items-center px-3 py-2 text-sm bg-gray-50 rounded-md">
                <span className="hidden sm:inline">Page </span>
                {pagination.page} <span className="hidden sm:inline">of {pagination.totalPages}</span>
                <span className="sm:hidden">/{pagination.totalPages}</span>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={!pagination.hasNextPage || loading}
              >
                <span className="hidden sm:inline">Next</span>
                <ChevronRight className="w-4 h-4 sm:ml-1" />
              </Button>
            </div>
          </div>
        )}

        {students.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No students found. {searchTerm || selectedClass || selectedGender ? 'Try adjusting your search criteria.' : 'Add your first student to get started.'}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
