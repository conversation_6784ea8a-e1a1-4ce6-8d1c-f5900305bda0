import { describe, it, expect } from 'vitest'
import { calculateGrade, getGradePoints, calculateGPA } from '../grading'

describe('Grading System', () => {
  describe('calculateGrade', () => {
    it('should return A+ for 90% and above', () => {
      expect(calculateGrade(90)).toBe('A+')
      expect(calculateGrade(95)).toBe('A+')
      expect(calculateGrade(100)).toBe('A+')
    })

    it('should return A for 80-89%', () => {
      expect(calculateGrade(80)).toBe('A')
      expect(calculateGrade(85)).toBe('A')
      expect(calculateGrade(89)).toBe('A')
    })

    it('should return B+ for 70-79%', () => {
      expect(calculateGrade(70)).toBe('B+')
      expect(calculateGrade(75)).toBe('B+')
      expect(calculateGrade(79)).toBe('B+')
    })

    it('should return B for 60-69%', () => {
      expect(calculateGrade(60)).toBe('B')
      expect(calculateGrade(65)).toBe('B')
      expect(calculateGrade(69)).toBe('B')
    })

    it('should return C+ for 50-59%', () => {
      expect(calculateGrade(50)).toBe('C+')
      expect(calculateGrade(55)).toBe('C+')
      expect(calculateGrade(59)).toBe('C+')
    })

    it('should return C for 40-49%', () => {
      expect(calculateGrade(40)).toBe('C')
      expect(calculateGrade(45)).toBe('C')
      expect(calculateGrade(49)).toBe('C')
    })

    it('should return D for 30-39%', () => {
      expect(calculateGrade(30)).toBe('D')
      expect(calculateGrade(35)).toBe('D')
      expect(calculateGrade(39)).toBe('D')
    })

    it('should return F for below 30%', () => {
      expect(calculateGrade(0)).toBe('F')
      expect(calculateGrade(15)).toBe('F')
      expect(calculateGrade(29)).toBe('F')
    })

    it('should handle edge cases', () => {
      expect(calculateGrade(-5)).toBe('F')
      expect(calculateGrade(105)).toBe('A+')
    })

    it('should handle decimal values', () => {
      expect(calculateGrade(89.9)).toBe('A')
      expect(calculateGrade(90.1)).toBe('A+')
      expect(calculateGrade(79.5)).toBe('B+')
    })
  })

  describe('getGradePoints', () => {
    it('should return correct grade points', () => {
      expect(getGradePoints('A+')).toBe(4.0)
      expect(getGradePoints('A')).toBe(3.7)
      expect(getGradePoints('B+')).toBe(3.3)
      expect(getGradePoints('B')).toBe(3.0)
      expect(getGradePoints('C+')).toBe(2.7)
      expect(getGradePoints('C')).toBe(2.3)
      expect(getGradePoints('D')).toBe(2.0)
      expect(getGradePoints('F')).toBe(0.0)
    })

    it('should handle invalid grades', () => {
      expect(getGradePoints('X' as any)).toBe(0.0)
      expect(getGradePoints('' as any)).toBe(0.0)
    })
  })

  describe('calculateGPA', () => {
    it('should calculate GPA correctly', () => {
      const grades = [
        { grade: 'A+', creditHours: 3 },
        { grade: 'A', creditHours: 4 },
        { grade: 'B+', creditHours: 2 }
      ]
      
      // (4.0*3 + 3.7*4 + 3.3*2) / (3+4+2) = (12 + 14.8 + 6.6) / 9 = 33.4 / 9 ≈ 3.71
      const gpa = calculateGPA(grades)
      expect(gpa).toBeCloseTo(3.71, 2)
    })

    it('should return 0 for empty grades array', () => {
      expect(calculateGPA([])).toBe(0)
    })

    it('should handle zero credit hours', () => {
      const grades = [
        { grade: 'A+', creditHours: 0 },
        { grade: 'A', creditHours: 3 }
      ]
      
      const gpa = calculateGPA(grades)
      expect(gpa).toBe(3.7) // Only the A grade should count
    })

    it('should handle all F grades', () => {
      const grades = [
        { grade: 'F', creditHours: 3 },
        { grade: 'F', creditHours: 4 }
      ]
      
      const gpa = calculateGPA(grades)
      expect(gpa).toBe(0)
    })
  })
})
