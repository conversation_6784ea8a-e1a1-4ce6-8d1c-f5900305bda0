import { describe, it, expect } from 'vitest'
import { 
  validateMarkEntry, 
  validateBulkMarkEntry, 
  validateGradeCalculation,
  formatValidationErrors,
  groupErrorsByStudent,
  validatePattern,
  VALIDATION_PATTERNS
} from '../marks-validation'

describe('Marks Validation', () => {
  describe('validateMarkEntry', () => {
    it('should validate correct mark entry', () => {
      const result = validateMarkEntry('student1', 'exam1', 85, 100, 'Good work')
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject negative marks', () => {
      const result = validateMarkEntry('student1', 'exam1', -5, 100)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toContain('cannot be negative')
    })

    it('should reject marks exceeding maximum', () => {
      const result = validateMarkEntry('student1', 'exam1', 105, 100)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toContain('cannot exceed maximum marks')
    })

    it('should reject empty student ID', () => {
      const result = validateMarkEntry('', 'exam1', 85, 100)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].field).toBe('studentId')
    })

    it('should reject empty exam ID', () => {
      const result = validateMarkEntry('student1', '', 85, 100)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].field).toBe('examId')
    })

    it('should reject marks with too many decimal places', () => {
      const result = validateMarkEntry('student1', 'exam1', 85.123, 100)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toContain('at most 2 decimal places')
    })

    it('should accept marks with up to 2 decimal places', () => {
      const result = validateMarkEntry('student1', 'exam1', 85.12, 100)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject remarks that are too long', () => {
      const longRemarks = 'a'.repeat(501)
      const result = validateMarkEntry('student1', 'exam1', 85, 100, longRemarks)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toContain('cannot exceed 500 characters')
    })

    it('should accept valid remarks', () => {
      const result = validateMarkEntry('student1', 'exam1', 85, 100, 'Excellent performance')
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
  })

  describe('validateBulkMarkEntry', () => {
    it('should validate correct bulk entry', () => {
      const marksData = [
        { studentId: 'student1', obtainedMarks: 85, remarks: 'Good' },
        { studentId: 'student2', obtainedMarks: 92, remarks: 'Excellent' }
      ]
      
      const result = validateBulkMarkEntry('exam1', 100, marksData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject empty marks data', () => {
      const result = validateBulkMarkEntry('exam1', 100, [])
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toContain('At least one mark entry is required')
    })

    it('should reject duplicate student IDs', () => {
      const marksData = [
        { studentId: 'student1', obtainedMarks: 85 },
        { studentId: 'student1', obtainedMarks: 92 }
      ]
      
      const result = validateBulkMarkEntry('exam1', 100, marksData)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.message.includes('Duplicate student ID'))).toBe(true)
    })

    it('should validate individual entries in bulk', () => {
      const marksData = [
        { studentId: 'student1', obtainedMarks: 85 },
        { studentId: 'student2', obtainedMarks: 105 } // Invalid - exceeds max
      ]
      
      const result = validateBulkMarkEntry('exam1', 100, marksData)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.message.includes('cannot exceed maximum marks'))).toBe(true)
    })

    it('should reject empty exam ID', () => {
      const marksData = [
        { studentId: 'student1', obtainedMarks: 85 }
      ]
      
      const result = validateBulkMarkEntry('', 100, marksData)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].field).toBe('examId')
    })
  })

  describe('validateGradeCalculation', () => {
    it('should validate correct grade calculation', () => {
      const result = validateGradeCalculation(85, 100)
      expect(result.isValid).toBe(true)
      expect(result.percentage).toBe(85)
    })

    it('should reject zero or negative max marks', () => {
      const result = validateGradeCalculation(85, 0)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Maximum marks must be greater than 0')
    })

    it('should reject negative obtained marks', () => {
      const result = validateGradeCalculation(-5, 100)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('cannot be negative')
    })

    it('should reject obtained marks exceeding maximum', () => {
      const result = validateGradeCalculation(105, 100)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('cannot exceed maximum marks')
    })

    it('should calculate percentage correctly', () => {
      const result = validateGradeCalculation(75, 100)
      expect(result.isValid).toBe(true)
      expect(result.percentage).toBe(75)
    })

    it('should handle decimal calculations', () => {
      const result = validateGradeCalculation(33, 40)
      expect(result.isValid).toBe(true)
      expect(result.percentage).toBe(82.5)
    })
  })

  describe('formatValidationErrors', () => {
    it('should return empty string for no errors', () => {
      const result = formatValidationErrors([])
      expect(result).toBe('')
    })

    it('should return single error message', () => {
      const errors = [{ field: 'marks', message: 'Invalid marks' }]
      const result = formatValidationErrors(errors)
      expect(result).toBe('Invalid marks')
    })

    it('should format multiple errors', () => {
      const errors = [
        { field: 'marks', message: 'Invalid marks' },
        { field: 'student', message: 'Student not found' }
      ]
      const result = formatValidationErrors(errors)
      expect(result).toContain('Multiple errors found:')
      expect(result).toContain('• Invalid marks')
      expect(result).toContain('• Student not found')
    })
  })

  describe('groupErrorsByStudent', () => {
    it('should group errors by student ID', () => {
      const errors = [
        { field: 'marks', message: 'Invalid marks', studentId: 'student1' },
        { field: 'remarks', message: 'Too long', studentId: 'student1' },
        { field: 'marks', message: 'Negative marks', studentId: 'student2' },
        { field: 'exam', message: 'Exam not found' } // No studentId
      ]

      const grouped = groupErrorsByStudent(errors)
      
      expect(grouped['student1']).toHaveLength(2)
      expect(grouped['student2']).toHaveLength(1)
      expect(grouped['general']).toHaveLength(1)
    })

    it('should handle empty errors array', () => {
      const grouped = groupErrorsByStudent([])
      expect(Object.keys(grouped)).toHaveLength(0)
    })
  })

  describe('validatePattern', () => {
    it('should validate student ID pattern', () => {
      const result = validatePattern('STU001', VALIDATION_PATTERNS.STUDENT_ID, 'studentId')
      expect(result).toBeNull()
    })

    it('should reject invalid student ID pattern', () => {
      const result = validatePattern('STU@001', VALIDATION_PATTERNS.STUDENT_ID, 'studentId')
      expect(result).not.toBeNull()
      expect(result?.message).toContain('Invalid studentId format')
    })

    it('should validate exam ID pattern', () => {
      const result = validatePattern('EXAM_001', VALIDATION_PATTERNS.EXAM_ID, 'examId')
      expect(result).toBeNull()
    })

    it('should validate marks format pattern', () => {
      expect(validatePattern('85', VALIDATION_PATTERNS.MARKS_FORMAT, 'marks')).toBeNull()
      expect(validatePattern('85.5', VALIDATION_PATTERNS.MARKS_FORMAT, 'marks')).toBeNull()
      expect(validatePattern('85.50', VALIDATION_PATTERNS.MARKS_FORMAT, 'marks')).toBeNull()
      
      // Invalid formats
      expect(validatePattern('85.555', VALIDATION_PATTERNS.MARKS_FORMAT, 'marks')).not.toBeNull()
      expect(validatePattern('abc', VALIDATION_PATTERNS.MARKS_FORMAT, 'marks')).not.toBeNull()
      expect(validatePattern('85.', VALIDATION_PATTERNS.MARKS_FORMAT, 'marks')).not.toBeNull()
    })
  })

  describe('Integration Tests', () => {
    it('should handle complex validation scenario', () => {
      const marksData = [
        { studentId: 'STU001', obtainedMarks: 85.5, remarks: 'Good work' },
        { studentId: 'STU002', obtainedMarks: -5, remarks: 'Invalid' }, // Negative marks
        { studentId: 'STU003', obtainedMarks: 105, remarks: 'Exceeds max' }, // Exceeds max
        { studentId: 'STU001', obtainedMarks: 90, remarks: 'Duplicate' } // Duplicate student
      ]

      const result = validateBulkMarkEntry('EXAM001', 100, marksData)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      
      // Should have errors for negative marks, exceeding max, and duplicate student
      const errorMessages = result.errors.map(e => e.message)
      expect(errorMessages.some(msg => msg.includes('negative'))).toBe(true)
      expect(errorMessages.some(msg => msg.includes('exceed'))).toBe(true)
      expect(errorMessages.some(msg => msg.includes('Duplicate'))).toBe(true)
    })

    it('should pass validation for perfect bulk entry', () => {
      const marksData = [
        { studentId: 'STU001', obtainedMarks: 85.5, remarks: 'Good work' },
        { studentId: 'STU002', obtainedMarks: 92.0, remarks: 'Excellent' },
        { studentId: 'STU003', obtainedMarks: 78.25, remarks: 'Well done' }
      ]

      const result = validateBulkMarkEntry('EXAM001', 100, marksData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
  })
})
