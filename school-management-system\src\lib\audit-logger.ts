import { prisma } from '@/lib/db'

export type AuditAction = 
  | 'CREATE' 
  | 'UPDATE' 
  | 'DELETE' 
  | 'LOGIN' 
  | 'LOGOUT' 
  | 'VIEW' 
  | 'EXPORT' 
  | 'IMPORT'

export type AuditEntity = 
  | 'USER' 
  | 'STUDENT' 
  | 'TEACHER' 
  | 'CLASS' 
  | 'SUBJECT' 
  | 'EXAM' 
  | 'MARK' 
  | 'ATTENDANCE' 
  | 'REPORT_CARD' 
  | 'TERM'

export interface AuditLogData {
  userId: string
  action: AuditAction
  entity: AuditEntity
  entityId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  metadata?: Record<string, any>
}

/**
 * Log an audit event
 */
export async function logAuditEvent(data: AuditLogData): Promise<void> {
  try {
    await prisma.auditLog.create({
      data: {
        userId: data.userId,
        action: data.action,
        entity: data.entity,
        entityId: data.entityId,
        details: data.details || {},
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        metadata: data.metadata || {},
        timestamp: new Date()
      }
    })
  } catch (error) {
    console.error('Error logging audit event:', error)
    // Don't throw error to avoid breaking the main operation
  }
}

/**
 * Log user authentication events
 */
export async function logAuthEvent(
  userId: string,
  action: 'LOGIN' | 'LOGOUT',
  ipAddress?: string,
  userAgent?: string,
  metadata?: Record<string, any>
): Promise<void> {
  await logAuditEvent({
    userId,
    action,
    entity: 'USER',
    entityId: userId,
    ipAddress,
    userAgent,
    metadata
  })
}

/**
 * Log data modification events
 */
export async function logDataEvent(
  userId: string,
  action: AuditAction,
  entity: AuditEntity,
  entityId: string,
  details?: Record<string, any>,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logAuditEvent({
    userId,
    action,
    entity,
    entityId,
    details,
    ipAddress,
    userAgent
  })
}

/**
 * Log marks entry/update
 */
export async function logMarksEvent(
  userId: string,
  action: 'CREATE' | 'UPDATE' | 'DELETE',
  markId: string,
  studentId: string,
  examId: string,
  oldMarks?: number,
  newMarks?: number,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  const details: Record<string, any> = {
    studentId,
    examId
  }

  if (action === 'UPDATE' && oldMarks !== undefined && newMarks !== undefined) {
    details.oldMarks = oldMarks
    details.newMarks = newMarks
    details.change = newMarks - oldMarks
  } else if (action === 'CREATE' && newMarks !== undefined) {
    details.marks = newMarks
  }

  await logAuditEvent({
    userId,
    action,
    entity: 'MARK',
    entityId: markId,
    details,
    ipAddress,
    userAgent
  })
}

/**
 * Log attendance events
 */
export async function logAttendanceEvent(
  userId: string,
  action: 'CREATE' | 'UPDATE',
  attendanceId: string,
  studentId: string,
  date: Date,
  oldStatus?: string,
  newStatus?: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  const details: Record<string, any> = {
    studentId,
    date: date.toISOString()
  }

  if (action === 'UPDATE' && oldStatus && newStatus) {
    details.oldStatus = oldStatus
    details.newStatus = newStatus
  } else if (action === 'CREATE' && newStatus) {
    details.status = newStatus
  }

  await logAuditEvent({
    userId,
    action,
    entity: 'ATTENDANCE',
    entityId: attendanceId,
    details,
    ipAddress,
    userAgent
  })
}

/**
 * Log report generation events
 */
export async function logReportEvent(
  userId: string,
  action: 'CREATE' | 'VIEW' | 'EXPORT',
  reportType: 'REPORT_CARD' | 'CLASS_REPORT' | 'ANALYTICS',
  entityId?: string,
  details?: Record<string, any>,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logAuditEvent({
    userId,
    action,
    entity: 'REPORT_CARD',
    entityId,
    details: {
      reportType,
      ...details
    },
    ipAddress,
    userAgent
  })
}

/**
 * Get audit logs with filtering
 */
export async function getAuditLogs(filters: {
  userId?: string
  action?: AuditAction
  entity?: AuditEntity
  entityId?: string
  startDate?: Date
  endDate?: Date
  limit?: number
  offset?: number
}) {
  const where: any = {}

  if (filters.userId) where.userId = filters.userId
  if (filters.action) where.action = filters.action
  if (filters.entity) where.entity = filters.entity
  if (filters.entityId) where.entityId = filters.entityId
  
  if (filters.startDate || filters.endDate) {
    where.timestamp = {}
    if (filters.startDate) where.timestamp.gte = filters.startDate
    if (filters.endDate) where.timestamp.lte = filters.endDate
  }

  const [logs, total] = await Promise.all([
    prisma.auditLog.findMany({
      where,
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: { timestamp: 'desc' },
      take: filters.limit || 50,
      skip: filters.offset || 0
    }),
    prisma.auditLog.count({ where })
  ])

  return { logs, total }
}

/**
 * Get user activity summary
 */
export async function getUserActivitySummary(
  userId: string,
  startDate?: Date,
  endDate?: Date
) {
  const where: any = { userId }
  
  if (startDate || endDate) {
    where.timestamp = {}
    if (startDate) where.timestamp.gte = startDate
    if (endDate) where.timestamp.lte = endDate
  }

  const logs = await prisma.auditLog.findMany({
    where,
    orderBy: { timestamp: 'desc' }
  })

  // Group by action
  const actionCounts = logs.reduce((acc, log) => {
    acc[log.action] = (acc[log.action] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Group by entity
  const entityCounts = logs.reduce((acc, log) => {
    acc[log.entity] = (acc[log.entity] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Get recent activities
  const recentActivities = logs.slice(0, 10)

  return {
    totalActivities: logs.length,
    actionCounts,
    entityCounts,
    recentActivities,
    firstActivity: logs[logs.length - 1]?.timestamp,
    lastActivity: logs[0]?.timestamp
  }
}

/**
 * Middleware helper to extract request info
 */
export function extractRequestInfo(request: Request) {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ipAddress = forwarded?.split(',')[0] || realIp || 'unknown'
  const userAgent = request.headers.get('user-agent') || 'unknown'

  return { ipAddress, userAgent }
}

/**
 * Clean up old audit logs (for maintenance)
 */
export async function cleanupOldAuditLogs(daysToKeep: number = 365): Promise<number> {
  try {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

    const result = await prisma.auditLog.deleteMany({
      where: {
        timestamp: {
          lt: cutoffDate
        }
      }
    })

    return result.count
  } catch (error) {
    console.error('Error cleaning up audit logs:', error)
    return 0
  }
}
