import { signIn, signOut, getSession } from 'next-auth/react'
import { getRoleDashboardUrl } from './navigation'

export interface LoginCredentials {
  email: string
  password: string
}

export interface LoginResult {
  success: boolean
  error?: string
  redirectUrl?: string
}

/**
 * Login function for admin, teacher, and student users
 * @param credentials - Email and password
 * @returns Promise with login result
 */
export async function loginUser(credentials: LoginCredentials): Promise<LoginResult> {
  try {
    // Validate input
    if (!credentials.email || !credentials.password) {
      return {
        success: false,
        error: 'Email and password are required'
      }
    }

    if (!credentials.email.includes('@')) {
      return {
        success: false,
        error: 'Please enter a valid email address'
      }
    }

    if (credentials.password.length < 6) {
      return {
        success: false,
        error: 'Password must be at least 6 characters long'
      }
    }

    // Attempt to sign in
    const result = await signIn('credentials', {
      email: credentials.email,
      password: credentials.password,
      redirect: false
    })

    if (result?.error) {
      return {
        success: false,
        error: 'Invalid email or password. Please try again.'
      }
    }

    // Get session to determine redirect URL
    const session = await getSession()
    const redirectUrl = session?.user?.role
      ? getRoleDashboardUrl(session.user.role)
      : '/'

    return {
      success: true,
      redirectUrl
    }
  } catch (error) {
    console.error('Login error:', error)
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.'
    }
  }
}

/**
 * Logout function
 * @param redirectUrl - Optional URL to redirect to after logout
 */
export async function logoutUser(redirectUrl: string = '/login'): Promise<void> {
  try {
    await signOut({ 
      redirect: true,
      callbackUrl: redirectUrl 
    })
  } catch (error) {
    console.error('Logout error:', error)
    // Force redirect even if signOut fails
    window.location.href = redirectUrl
  }
}

/**
 * Check if user is authenticated and has the required role
 * @param requiredRole - Required user role (optional)
 * @returns Promise with authentication status
 */
export async function checkAuth(requiredRole?: string): Promise<{
  isAuthenticated: boolean
  user?: { id: string; role: string; firstName: string; lastName: string; email: string }
  hasRequiredRole: boolean
}> {
  try {
    const session = await getSession()
    
    if (!session?.user) {
      return {
        isAuthenticated: false,
        hasRequiredRole: false
      }
    }

    const hasRequiredRole = !requiredRole || session.user.role === requiredRole

    return {
      isAuthenticated: true,
      user: session.user,
      hasRequiredRole
    }
  } catch (error) {
    console.error('Auth check error:', error)
    return {
      isAuthenticated: false,
      hasRequiredRole: false
    }
  }
}

/**
 * Get current user session
 * @returns Promise with user session or null
 */
export async function getCurrentUser() {
  try {
    const session = await getSession()
    return session?.user || null
  } catch (error) {
    console.error('Get current user error:', error)
    return null
  }
}

/**
 * Demo credentials for testing
 */
export const DEMO_CREDENTIALS = {
  admin: {
    email: '<EMAIL>',
    password: 'Admin@12345',
    role: 'ADMIN'
  },
  teacher: {
    email: '<EMAIL>',
    password: 'Teacher@12345',
    role: 'TEACHER'
  },
  student: {
    email: '<EMAIL>',
    password: 'Student@12345',
    role: 'STUDENT'
  }
} as const

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate password strength
 */
export function isValidPassword(password: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  if (password.length < 6) {
    errors.push('Password must be at least 6 characters long')
  }
  
  if (password.length > 128) {
    errors.push('Password must be less than 128 characters')
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Generate a secure random password
 */
export function generateSecurePassword(length: number = 12): string {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const numbers = '0123456789'
  const symbols = '!@#$%^&*(),.?":{}|<>'
  
  const allChars = uppercase + lowercase + numbers + symbols
  let password = ''
  
  // Ensure at least one character from each category
  password += uppercase[Math.floor(Math.random() * uppercase.length)]
  password += lowercase[Math.floor(Math.random() * lowercase.length)]
  password += numbers[Math.floor(Math.random() * numbers.length)]
  password += symbols[Math.floor(Math.random() * symbols.length)]
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)]
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('')
}
