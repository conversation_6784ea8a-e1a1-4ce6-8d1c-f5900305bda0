import nodemailer from 'nodemailer'

// Email configuration
const emailConfig = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD
  }
}

// Create transporter
const createTransporter = () => {
  if (!process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
    console.warn('Email service not configured. SMTP credentials missing.')
    return null
  }

  return nodemailer.createTransporter(emailConfig)
}

// Email templates
export const emailTemplates = {
  marksPublished: {
    subject: 'New Marks Published - {{examName}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #2563eb; color: white; padding: 20px; text-align: center;">
          <h1>Advance School</h1>
          <h2>New Marks Published</h2>
        </div>
        
        <div style="padding: 20px;">
          <p>Dear {{studentName}},</p>
          
          <p>Your marks for <strong>{{examName}}</strong> have been published.</p>
          
          <div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin: 0 0 10px 0; color: #374151;">Exam Details:</h3>
            <p><strong>Subject:</strong> {{subjectName}}</p>
            <p><strong>Exam:</strong> {{examName}}</p>
            <p><strong>Obtained Marks:</strong> {{obtainedMarks}}/{{maxMarks}}</p>
            <p><strong>Percentage:</strong> {{percentage}}%</p>
            <p><strong>Grade:</strong> {{grade}}</p>
          </div>
          
          {{#if remarks}}
          <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin: 0 0 10px 0; color: #92400e;">Teacher's Remarks:</h3>
            <p style="color: #78350f;">{{remarks}}</p>
          </div>
          {{/if}}
          
          <p>You can view your detailed marks by logging into the student portal.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{portalUrl}}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              View Marks
            </a>
          </div>
          
          <p>Best regards,<br>Advance School Administration</p>
        </div>
        
        <div style="background: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p>This is an automated message. Please do not reply to this email.</p>
        </div>
      </div>
    `
  },

  reportCardGenerated: {
    subject: 'Report Card Available - {{termName}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #2563eb; color: white; padding: 20px; text-align: center;">
          <h1>Advance School</h1>
          <h2>Report Card Available</h2>
        </div>
        
        <div style="padding: 20px;">
          <p>Dear {{studentName}},</p>
          
          <p>Your report card for <strong>{{termName}}</strong> is now available for download.</p>
          
          <div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin: 0 0 10px 0; color: #374151;">Performance Summary:</h3>
            <p><strong>Term:</strong> {{termName}} ({{academicYear}})</p>
            <p><strong>Overall Percentage:</strong> {{percentage}}%</p>
            <p><strong>Grade:</strong> {{grade}}</p>
            <p><strong>Class Rank:</strong> {{rank}}/{{totalStudents}}</p>
          </div>
          
          <p>You can download your report card from the student portal.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{portalUrl}}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Download Report Card
            </a>
          </div>
          
          <p>Best regards,<br>Advance School Administration</p>
        </div>
        
        <div style="background: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p>This is an automated message. Please do not reply to this email.</p>
        </div>
      </div>
    `
  },

  attendanceAlert: {
    subject: 'Attendance Alert - {{studentName}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #dc2626; color: white; padding: 20px; text-align: center;">
          <h1>Advance School</h1>
          <h2>Attendance Alert</h2>
        </div>
        
        <div style="padding: 20px;">
          <p>Dear Parent/Guardian,</p>
          
          <p>This is to inform you that <strong>{{studentName}}</strong> has low attendance.</p>
          
          <div style="background: #fef2f2; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626;">
            <h3 style="margin: 0 0 10px 0; color: #991b1b;">Attendance Details:</h3>
            <p><strong>Student:</strong> {{studentName}} ({{admissionNo}})</p>
            <p><strong>Class:</strong> {{className}} - {{sectionName}}</p>
            <p><strong>Current Attendance:</strong> {{attendancePercentage}}%</p>
            <p><strong>Present Days:</strong> {{presentDays}}/{{totalDays}}</p>
          </div>
          
          <p>Please ensure regular attendance to avoid academic difficulties.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{portalUrl}}" style="background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              View Attendance Details
            </a>
          </div>
          
          <p>Best regards,<br>Advance School Administration</p>
        </div>
        
        <div style="background: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p>This is an automated message. Please do not reply to this email.</p>
        </div>
      </div>
    `
  }
}

// Template rendering function
function renderTemplate(template: string, data: Record<string, any>): string {
  let rendered = template
  
  // Simple template replacement (basic Handlebars-like syntax)
  Object.entries(data).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g')
    rendered = rendered.replace(regex, String(value))
  })
  
  // Handle conditional blocks {{#if condition}}...{{/if}}
  rendered = rendered.replace(/{{#if\s+(\w+)}}([\s\S]*?){{\/if}}/g, (match, condition, content) => {
    return data[condition] ? content : ''
  })
  
  return rendered
}

// Email sending functions
export interface EmailOptions {
  to: string | string[]
  subject: string
  html: string
  attachments?: Array<{
    filename: string
    content: Buffer
    contentType: string
  }>
}

export async function sendEmail(options: EmailOptions): Promise<boolean> {
  const transporter = createTransporter()
  
  if (!transporter) {
    console.warn('Email service not configured. Skipping email send.')
    return false
  }

  try {
    const mailOptions = {
      from: `"Advance School" <${process.env.SMTP_USER}>`,
      to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
      subject: options.subject,
      html: options.html,
      attachments: options.attachments
    }

    const result = await transporter.sendMail(mailOptions)
    console.log('Email sent successfully:', result.messageId)
    return true
  } catch (error) {
    console.error('Error sending email:', error)
    return false
  }
}

// Specific notification functions
export async function sendMarksNotification(data: {
  studentEmail: string
  studentName: string
  examName: string
  subjectName: string
  obtainedMarks: number
  maxMarks: number
  percentage: number
  grade: string
  remarks?: string
  portalUrl: string
}): Promise<boolean> {
  const template = emailTemplates.marksPublished
  const subject = renderTemplate(template.subject, data)
  const html = renderTemplate(template.html, data)

  return sendEmail({
    to: data.studentEmail,
    subject,
    html
  })
}

export async function sendReportCardNotification(data: {
  studentEmail: string
  studentName: string
  termName: string
  academicYear: string
  percentage: number
  grade: string
  rank: number
  totalStudents: number
  portalUrl: string
  pdfBuffer?: Buffer
}): Promise<boolean> {
  const template = emailTemplates.reportCardGenerated
  const subject = renderTemplate(template.subject, data)
  const html = renderTemplate(template.html, data)

  const emailOptions: EmailOptions = {
    to: data.studentEmail,
    subject,
    html
  }

  // Attach PDF if provided
  if (data.pdfBuffer) {
    emailOptions.attachments = [{
      filename: `report-card-${data.termName.replace(/\s+/g, '-')}.pdf`,
      content: data.pdfBuffer,
      contentType: 'application/pdf'
    }]
  }

  return sendEmail(emailOptions)
}

export async function sendAttendanceAlert(data: {
  parentEmail: string
  studentName: string
  admissionNo: string
  className: string
  sectionName: string
  attendancePercentage: number
  presentDays: number
  totalDays: number
  portalUrl: string
}): Promise<boolean> {
  const template = emailTemplates.attendanceAlert
  const subject = renderTemplate(template.subject, data)
  const html = renderTemplate(template.html, data)

  return sendEmail({
    to: data.parentEmail,
    subject,
    html
  })
}

// Bulk email functions
export async function sendBulkMarksNotifications(notifications: Array<{
  studentEmail: string
  studentName: string
  examName: string
  subjectName: string
  obtainedMarks: number
  maxMarks: number
  percentage: number
  grade: string
  remarks?: string
  portalUrl: string
}>): Promise<{ sent: number; failed: number }> {
  let sent = 0
  let failed = 0

  for (const notification of notifications) {
    const success = await sendMarksNotification(notification)
    if (success) {
      sent++
    } else {
      failed++
    }
    
    // Add small delay to avoid overwhelming the SMTP server
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  return { sent, failed }
}

// Email validation
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Environment validation
export function isEmailServiceConfigured(): boolean {
  return !!(process.env.SMTP_USER && process.env.SMTP_PASSWORD)
}

// Test email function
export async function sendTestEmail(to: string): Promise<boolean> {
  return sendEmail({
    to,
    subject: 'Test Email from Advance School',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2>Email Service Test</h2>
        <p>This is a test email to verify that the email service is working correctly.</p>
        <p>If you received this email, the email configuration is working properly.</p>
        <p>Best regards,<br>Advance School System</p>
      </div>
    `
  })
}
