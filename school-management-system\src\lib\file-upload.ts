import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

// File upload configuration
export const UPLOAD_CONFIG = {
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  allowedDocumentTypes: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv'
  ],
  uploadPaths: {
    profiles: 'uploads/profiles',
    documents: 'uploads/documents',
    bulk: 'uploads/bulk',
    temp: 'uploads/temp'
  }
} as const

export interface UploadResult {
  success: boolean
  filePath?: string
  fileName?: string
  fileSize?: number
  error?: string
}

export interface FileValidationResult {
  isValid: boolean
  error?: string
}

/**
 * Validate uploaded file
 */
export function validateFile(
  file: File,
  allowedTypes: string[],
  maxSize: number = UPLOAD_CONFIG.maxFileSize
): FileValidationResult {
  // Check file size
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size exceeds maximum allowed size of ${Math.round(maxSize / 1024 / 1024)}MB`
    }
  }

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
    }
  }

  // Check file name
  if (file.name.length > 255) {
    return {
      isValid: false,
      error: 'File name is too long (maximum 255 characters)'
    }
  }

  return { isValid: true }
}

/**
 * Generate unique filename
 */
export function generateFileName(originalName: string, prefix?: string): string {
  const extension = path.extname(originalName)
  const baseName = path.basename(originalName, extension)
  const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9-_]/g, '-')
  const uuid = uuidv4().split('-')[0] // Use first part of UUID for shorter names
  
  return prefix 
    ? `${prefix}-${sanitizedBaseName}-${uuid}${extension}`
    : `${sanitizedBaseName}-${uuid}${extension}`
}

/**
 * Ensure upload directory exists
 */
export async function ensureUploadDirectory(uploadPath: string): Promise<void> {
  const fullPath = path.join(process.cwd(), 'public', uploadPath)
  
  if (!existsSync(fullPath)) {
    await mkdir(fullPath, { recursive: true })
  }
}

/**
 * Save uploaded file
 */
export async function saveUploadedFile(
  file: File,
  uploadPath: string,
  fileName?: string
): Promise<UploadResult> {
  try {
    // Ensure directory exists
    await ensureUploadDirectory(uploadPath)

    // Generate filename if not provided
    const finalFileName = fileName || generateFileName(file.name)
    const filePath = path.join(uploadPath, finalFileName)
    const fullPath = path.join(process.cwd(), 'public', filePath)

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Save file
    await writeFile(fullPath, buffer)

    return {
      success: true,
      filePath: `/${filePath}`,
      fileName: finalFileName,
      fileSize: file.size
    }
  } catch (error) {
    console.error('Error saving file:', error)
    return {
      success: false,
      error: 'Failed to save file'
    }
  }
}

/**
 * Upload profile picture
 */
export async function uploadProfilePicture(file: File, userId: string): Promise<UploadResult> {
  // Validate file
  const validation = validateFile(file, UPLOAD_CONFIG.allowedImageTypes)
  if (!validation.isValid) {
    return {
      success: false,
      error: validation.error
    }
  }

  // Generate filename with user prefix
  const fileName = generateFileName(file.name, `profile-${userId}`)
  
  return saveUploadedFile(file, UPLOAD_CONFIG.uploadPaths.profiles, fileName)
}

/**
 * Upload document
 */
export async function uploadDocument(
  file: File, 
  category: 'student' | 'teacher' | 'admin' | 'general',
  userId?: string
): Promise<UploadResult> {
  // Validate file
  const validation = validateFile(file, UPLOAD_CONFIG.allowedDocumentTypes)
  if (!validation.isValid) {
    return {
      success: false,
      error: validation.error
    }
  }

  // Generate filename with category prefix
  const prefix = userId ? `${category}-${userId}` : category
  const fileName = generateFileName(file.name, prefix)
  
  return saveUploadedFile(file, UPLOAD_CONFIG.uploadPaths.documents, fileName)
}

/**
 * Upload bulk import file (CSV/Excel)
 */
export async function uploadBulkFile(file: File, type: 'students' | 'teachers' | 'marks'): Promise<UploadResult> {
  // Validate file
  const allowedTypes = [
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ]
  
  const validation = validateFile(file, allowedTypes)
  if (!validation.isValid) {
    return {
      success: false,
      error: validation.error
    }
  }

  // Generate filename with type prefix
  const fileName = generateFileName(file.name, `bulk-${type}`)
  
  return saveUploadedFile(file, UPLOAD_CONFIG.uploadPaths.bulk, fileName)
}

/**
 * Clean up old files (utility function for maintenance)
 */
export async function cleanupOldFiles(directory: string, maxAgeHours: number = 24): Promise<number> {
  try {
    const fs = await import('fs/promises')
    const fullPath = path.join(process.cwd(), 'public', directory)
    
    if (!existsSync(fullPath)) {
      return 0
    }

    const files = await fs.readdir(fullPath)
    const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000)
    let deletedCount = 0

    for (const file of files) {
      const filePath = path.join(fullPath, file)
      const stats = await fs.stat(filePath)
      
      if (stats.mtime.getTime() < cutoffTime) {
        await fs.unlink(filePath)
        deletedCount++
      }
    }

    return deletedCount
  } catch (error) {
    console.error('Error cleaning up files:', error)
    return 0
  }
}

/**
 * Get file info
 */
export async function getFileInfo(filePath: string): Promise<{
  exists: boolean
  size?: number
  mtime?: Date
  error?: string
}> {
  try {
    const fs = await import('fs/promises')
    const fullPath = path.join(process.cwd(), 'public', filePath)
    
    if (!existsSync(fullPath)) {
      return { exists: false }
    }

    const stats = await fs.stat(fullPath)
    return {
      exists: true,
      size: stats.size,
      mtime: stats.mtime
    }
  } catch (error) {
    return {
      exists: false,
      error: 'Error reading file info'
    }
  }
}

/**
 * Delete file
 */
export async function deleteFile(filePath: string): Promise<boolean> {
  try {
    const fs = await import('fs/promises')
    const fullPath = path.join(process.cwd(), 'public', filePath)
    
    if (existsSync(fullPath)) {
      await fs.unlink(fullPath)
      return true
    }
    
    return false
  } catch (error) {
    console.error('Error deleting file:', error)
    return false
  }
}

// File type utilities
export function getFileCategory(mimeType: string): 'image' | 'document' | 'unknown' {
  if (UPLOAD_CONFIG.allowedImageTypes.includes(mimeType)) {
    return 'image'
  }
  if (UPLOAD_CONFIG.allowedDocumentTypes.includes(mimeType)) {
    return 'document'
  }
  return 'unknown'
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
