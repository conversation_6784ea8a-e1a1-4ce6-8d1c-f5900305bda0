// Grade mapping configuration
export interface GradeConfig {
  A_PLUS: number; // >= 90
  A: number;     // >= 80
  B_PLUS: number; // >= 70
  B: number;     // >= 60
  C: number;     // >= 50
  D: number;     // >= 40
  E: number;     // < 40
}

// Default grade configuration
export const DEFAULT_GRADE_CONFIG: GradeConfig = {
  A_PLUS: 90,
  A: 80,
  B_PLUS: 70,
  B: 60,
  C: 50,
  D: 40,
  E: 0
}

/**
 * Calculate grade based on percentage
 */
export function calculateGrade(percentage: number, config: GradeConfig = DEFAULT_GRADE_CONFIG): string {
  if (percentage >= config.A_PLUS) return 'A+'
  if (percentage >= config.A) return 'A'
  if (percentage >= config.B_PLUS) return 'B+'
  if (percentage >= config.B) return 'B'
  if (percentage >= config.C) return 'C'
  if (percentage >= config.D) return 'D'
  return 'E'
}

/**
 * Calculate percentage from obtained marks and max marks
 */
export function calculatePercentage(obtainedMarks: number, maxMarks: number): number {
  if (maxMarks === 0) return 0
  return Math.round((obtainedMarks / maxMarks) * 100 * 100) / 100 // Round to 2 decimal places
}

/**
 * Calculate GPA based on grades
 */
export function calculateGPA(grades: string[], config: GradeConfig = DEFAULT_GRADE_CONFIG): number {
  if (grades.length === 0) return 0

  const gradePoints = grades.map(grade => {
    switch (grade) {
      case 'A+': return 4.0
      case 'A': return 3.7
      case 'B+': return 3.3
      case 'B': return 3.0
      case 'C': return 2.0
      case 'D': return 1.0
      case 'E': return 0.0
      default: return 0.0
    }
  })

  const totalPoints = gradePoints.reduce((sum, points) => sum + points, 0 as number)
  return Math.round((totalPoints / grades.length) * 100) / 100 // Round to 2 decimal places
}

/**
 * Calculate weighted GPA based on subject credits
 */
export function calculateWeightedGPA(
  grades: string[], 
  credits: number[] = [], 
  config: GradeConfig = DEFAULT_GRADE_CONFIG
): number {
  if (grades.length === 0) return 0

  // If no credits provided, use equal weight (default credit = 1)
  const subjectCredits = credits.length === grades.length ? credits : grades.map(() => 1)

  const gradePoints = grades.map(grade => {
    switch (grade) {
      case 'A+': return 4.0
      case 'A': return 3.7
      case 'B+': return 3.3
      case 'B': return 3.0
      case 'C': return 2.0
      case 'D': return 1.0
      case 'E': return 0.0
      default: return 0.0
    }
  })

  const totalWeightedPoints = gradePoints.reduce((sum, points, index) => {
    return sum + (points * subjectCredits[index])
  }, 0 as number)

  const totalCredits = subjectCredits.reduce((sum, credit) => sum + credit, 0)
  
  return Math.round((totalWeightedPoints / totalCredits) * 100) / 100 // Round to 2 decimal places
}

/**
 * Calculate attendance percentage
 */
export function calculateAttendancePercentage(presentDays: number, totalDays: number): number {
  if (totalDays === 0) return 0
  return Math.round((presentDays / totalDays) * 100 * 100) / 100 // Round to 2 decimal places
}

/**
 * Get grade color for UI display (text color)
 */
export function getGradeColor(grade: string): string {
  switch (grade) {
    case 'A+':
    case 'A':
      return 'text-green-600'
    case 'B+':
    case 'B':
      return 'text-blue-600'
    case 'C+':
    case 'C':
      return 'text-yellow-600'
    case 'D':
      return 'text-orange-600'
    case 'E':
    case 'F':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

/**
 * Get grade badge color for UI display (background + text + border)
 */
export function getGradeBadgeColor(grade: string): string {
  switch (grade) {
    case 'A+':
    case 'A':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'B+':
    case 'B':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'C+':
    case 'C':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'D':
      return 'bg-orange-100 text-orange-800 border-orange-200'
    case 'E':
    case 'F':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

/**
 * Get attendance status color for UI display
 */
export function getAttendanceColor(status: 'PRESENT' | 'ABSENT' | 'LATE' | 'HALF_DAY'): string {
  switch (status) {
    case 'PRESENT':
      return 'text-green-600'
    case 'ABSENT':
      return 'text-red-600'
    case 'LATE':
      return 'text-yellow-600'
    case 'HALF_DAY':
      return 'text-orange-600'
    default:
      return 'text-gray-600'
  }
}
