// Shared navigation configurations for different user roles

export const adminNavigation = [
  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },
  { name: 'Students', href: '/admin/students', icon: 'Users' },
  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },
  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },
  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },
  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },
  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },
  { name: 'Marks', href: '/admin/marks', icon: 'Award' },
  { name: 'Reports', href: '/admin/reports', icon: 'Download' },
  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },
];

export const teacherNavigation = [
  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },
  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },
  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },
  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },
  { name: 'Students', href: '/teacher/students', icon: 'Users' },
  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },
  { name: 'Profile', href: '/teacher/profile', icon: 'User' },
];

export const studentNavigation = [
  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },
  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },
  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },
  { name: 'Marks', href: '/student/marks', icon: 'Award' },
  { name: 'Reports', href: '/student/reports', icon: 'FileText' },
  { name: 'Profile', href: '/student/profile', icon: 'User' },
];

/**
 * Get the default dashboard URL for a user role
 */
export function getRoleDashboardUrl(role: string): string {
  switch (role) {
    case 'ADMIN':
      return '/admin'
    case 'TEACHER':
      return '/teacher'
    case 'STUDENT':
      return '/student'
    default:
      return '/'
  }
}

/**
 * Get navigation items for a user role
 */
export function getRoleNavigation(role: string) {
  switch (role) {
    case 'ADMIN':
      return adminNavigation
    case 'TEACHER':
      return teacherNavigation
    case 'STUDENT':
      return studentNavigation
    default:
      return []
  }
}