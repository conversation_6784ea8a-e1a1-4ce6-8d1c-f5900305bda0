import { chromium } from 'playwright'
import path from 'path'
import fs from 'fs'

export interface ReportCardData {
  student: {
    name: string
    admissionNo: string
    rollNumber: string
    class: string
    section: string
    academicYear: string
    dateOfBirth: string
    fatherName: string
    motherName: string
  }
  term: {
    name: string
    startDate: string
    endDate: string
    academicYear: string
  }
  marks: Array<{
    subject: string
    exam: string
    obtainedMarks: number
    maxMarks: number
    percentage: number
    grade: string
  }>
  summary: {
    totalMarks: number
    obtainedMarks: number
    percentage: number
    grade: string
    rank: number
    totalStudents: number
  }
  attendance: {
    totalDays: number
    presentDays: number
    absentDays: number
    percentage: number
  }
  remarks?: string
  generatedAt: string
}

export interface PDFGenerationOptions {
  format?: 'A4' | 'Letter'
  orientation?: 'portrait' | 'landscape'
  margin?: {
    top?: string
    right?: string
    bottom?: string
    left?: string
  }
  displayHeaderFooter?: boolean
  headerTemplate?: string
  footerTemplate?: string
}

/**
 * Generate PDF from HTML content using Playwright
 */
export async function generatePDFFromHTML(
  htmlContent: string,
  options: PDFGenerationOptions = {}
): Promise<Buffer> {
  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // Set content
    await page.setContent(htmlContent, { waitUntil: 'networkidle' })

    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: options.format || 'A4',
      landscape: options.orientation === 'landscape',
      margin: {
        top: options.margin?.top || '1cm',
        right: options.margin?.right || '1cm',
        bottom: options.margin?.bottom || '1cm',
        left: options.margin?.left || '1cm'
      },
      displayHeaderFooter: options.displayHeaderFooter || false,
      headerTemplate: options.headerTemplate || '',
      footerTemplate: options.footerTemplate || '',
      printBackground: true
    })

    return Buffer.from(pdfBuffer)
  } finally {
    await browser.close()
  }
}

/**
 * Generate report card HTML template
 */
export function generateReportCardHTML(data: ReportCardData): string {
  const { student, term, marks, summary, attendance, remarks, generatedAt } = data

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Card - ${student.name}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .report-card {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .school-name {
            font-size: 28px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 5px;
        }
        
        .school-address {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .report-title {
            font-size: 20px;
            font-weight: bold;
            color: #374151;
            margin-top: 15px;
        }
        
        .student-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
        }
        
        .info-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px dotted #d1d5db;
        }
        
        .info-label {
            font-weight: 600;
            color: #374151;
        }
        
        .info-value {
            color: #6b7280;
        }
        
        .marks-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .marks-table th,
        .marks-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #d1d5db;
        }
        
        .marks-table th {
            background: #2563eb;
            color: white;
            font-weight: 600;
        }
        
        .marks-table tr:nth-child(even) {
            background: #f8fafc;
        }
        
        .grade-cell {
            font-weight: bold;
        }
        
        .grade-A { color: #059669; }
        .grade-B { color: #0284c7; }
        .grade-C { color: #d97706; }
        .grade-D { color: #dc2626; }
        .grade-F { color: #dc2626; }
        
        .summary-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            text-align: center;
        }
        
        .summary-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 15px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .stat-item {
            padding: 10px;
            background: #f1f5f9;
            border-radius: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 2px;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #1e293b;
        }
        
        .remarks-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #fef3c7;
            border-left: 4px solid #f59e0b;
            border-radius: 4px;
        }
        
        .remarks-title {
            font-weight: 600;
            color: #92400e;
            margin-bottom: 10px;
        }
        
        .remarks-text {
            color: #78350f;
            line-height: 1.6;
        }
        
        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
            color: #6b7280;
            font-size: 12px;
        }
        
        .signature-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 40px;
            margin-top: 40px;
            margin-bottom: 20px;
        }
        
        .signature-box {
            text-align: center;
            padding-top: 40px;
            border-top: 1px solid #374151;
        }
        
        .signature-label {
            font-size: 12px;
            color: #6b7280;
        }
        
        @media print {
            body { margin: 0; }
            .report-card { padding: 10px; }
        }
    </style>
</head>
<body>
    <div class="report-card">
        <!-- Header -->
        <div class="header">
            <div class="school-name">Advance School</div>
            <div class="school-address">123 Education Street, Learning City, State 12345</div>
            <div class="report-title">Academic Report Card</div>
            <div style="margin-top: 10px; font-size: 14px; color: #666;">
                ${term.name} - Academic Year ${term.academicYear}
            </div>
        </div>

        <!-- Student Information -->
        <div class="student-info">
            <div class="info-group">
                <div class="info-item">
                    <span class="info-label">Student Name:</span>
                    <span class="info-value">${student.name}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Admission No:</span>
                    <span class="info-value">${student.admissionNo}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Roll Number:</span>
                    <span class="info-value">${student.rollNumber}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Class:</span>
                    <span class="info-value">${student.class} - ${student.section}</span>
                </div>
            </div>
            <div class="info-group">
                <div class="info-item">
                    <span class="info-label">Date of Birth:</span>
                    <span class="info-value">${new Date(student.dateOfBirth).toLocaleDateString()}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Father's Name:</span>
                    <span class="info-value">${student.fatherName}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Mother's Name:</span>
                    <span class="info-value">${student.motherName}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Academic Year:</span>
                    <span class="info-value">${student.academicYear}</span>
                </div>
            </div>
        </div>

        <!-- Marks Table -->
        <table class="marks-table">
            <thead>
                <tr>
                    <th>Subject</th>
                    <th>Exam</th>
                    <th>Max Marks</th>
                    <th>Obtained Marks</th>
                    <th>Percentage</th>
                    <th>Grade</th>
                </tr>
            </thead>
            <tbody>
                ${marks.map(mark => `
                    <tr>
                        <td>${mark.subject}</td>
                        <td>${mark.exam}</td>
                        <td>${mark.maxMarks}</td>
                        <td>${mark.obtainedMarks}</td>
                        <td>${mark.percentage.toFixed(1)}%</td>
                        <td class="grade-cell grade-${mark.grade.replace('+', '')}">${mark.grade}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>

        <!-- Summary Section -->
        <div class="summary-section">
            <div class="summary-card">
                <div class="summary-title">Academic Performance</div>
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="stat-label">Total Marks</div>
                        <div class="stat-value">${summary.totalMarks}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Obtained</div>
                        <div class="stat-value">${summary.obtainedMarks}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Percentage</div>
                        <div class="stat-value">${summary.percentage.toFixed(1)}%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Grade</div>
                        <div class="stat-value grade-${summary.grade.replace('+', '')}">${summary.grade}</div>
                    </div>
                </div>
            </div>
            
            <div class="summary-card">
                <div class="summary-title">Attendance & Ranking</div>
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="stat-label">Present Days</div>
                        <div class="stat-value">${attendance.presentDays}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Total Days</div>
                        <div class="stat-value">${attendance.totalDays}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Attendance</div>
                        <div class="stat-value">${attendance.percentage.toFixed(1)}%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Class Rank</div>
                        <div class="stat-value">${summary.rank}/${summary.totalStudents}</div>
                    </div>
                </div>
            </div>
        </div>

        ${remarks ? `
        <!-- Remarks Section -->
        <div class="remarks-section">
            <div class="remarks-title">Teacher's Remarks</div>
            <div class="remarks-text">${remarks}</div>
        </div>
        ` : ''}

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-label">Class Teacher</div>
            </div>
            <div class="signature-box">
                <div class="signature-label">Principal</div>
            </div>
            <div class="signature-box">
                <div class="signature-label">Parent/Guardian</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Generated on ${new Date(generatedAt).toLocaleDateString()} at ${new Date(generatedAt).toLocaleTimeString()}</p>
            <p>This is a computer-generated document and does not require a signature.</p>
        </div>
    </div>
</body>
</html>
  `
}

/**
 * Generate and save report card PDF
 */
export async function generateReportCardPDF(
  data: ReportCardData,
  outputPath?: string
): Promise<{ buffer: Buffer; filePath?: string }> {
  const htmlContent = generateReportCardHTML(data)
  const pdfBuffer = await generatePDFFromHTML(htmlContent)

  if (outputPath) {
    // Ensure directory exists
    const dir = path.dirname(outputPath)
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }

    // Save to file
    fs.writeFileSync(outputPath, pdfBuffer)
    return { buffer: pdfBuffer, filePath: outputPath }
  }

  return { buffer: pdfBuffer }
}

/**
 * Generate class performance report HTML
 */
export function generateClassReportHTML(classData: {
  class: { name: string; section: string; academicYear: string }
  term: { name: string; startDate: string; endDate: string }
  students: Array<{
    name: string
    admissionNo: string
    rollNumber: string
    totalMarks: number
    obtainedMarks: number
    percentage: number
    grade: string
    rank: number
    attendancePercentage: number
  }>
  summary: {
    totalStudents: number
    averagePercentage: number
    passedStudents: number
    failedStudents: number
    highestPercentage: number
    lowestPercentage: number
  }
  generatedAt: string
}): string {
  const { class: classInfo, term, students, summary, generatedAt } = classData

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Class Performance Report - ${classInfo.name} ${classInfo.section}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .report {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .school-name {
            font-size: 28px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 5px;
        }
        
        .report-title {
            font-size: 20px;
            font-weight: bold;
            color: #374151;
            margin-top: 15px;
        }
        
        .class-info {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            padding: 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            text-align: center;
        }
        
        .summary-value {
            font-size: 24px;
            font-weight: bold;
            color: #1e40af;
        }
        
        .summary-label {
            font-size: 12px;
            color: #6b7280;
            margin-top: 5px;
        }
        
        .students-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            font-size: 12px;
        }
        
        .students-table th,
        .students-table td {
            padding: 8px;
            text-align: center;
            border: 1px solid #d1d5db;
        }
        
        .students-table th {
            background: #2563eb;
            color: white;
            font-weight: 600;
        }
        
        .students-table tr:nth-child(even) {
            background: #f8fafc;
        }
        
        .grade-A { color: #059669; font-weight: bold; }
        .grade-B { color: #0284c7; font-weight: bold; }
        .grade-C { color: #d97706; font-weight: bold; }
        .grade-D { color: #dc2626; font-weight: bold; }
        .grade-F { color: #dc2626; font-weight: bold; }
        
        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
            color: #6b7280;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="report">
        <!-- Header -->
        <div class="header">
            <div class="school-name">Advance School</div>
            <div class="report-title">Class Performance Report</div>
        </div>

        <!-- Class Information -->
        <div class="class-info">
            <h2>${classInfo.name} - Section ${classInfo.section}</h2>
            <p>${term.name} (${new Date(term.startDate).toLocaleDateString()} - ${new Date(term.endDate).toLocaleDateString()})</p>
            <p>Academic Year: ${classInfo.academicYear}</p>
        </div>

        <!-- Summary Statistics -->
        <div class="summary-grid">
            <div class="summary-card">
                <div class="summary-value">${summary.totalStudents}</div>
                <div class="summary-label">Total Students</div>
            </div>
            <div class="summary-card">
                <div class="summary-value">${summary.averagePercentage.toFixed(1)}%</div>
                <div class="summary-label">Class Average</div>
            </div>
            <div class="summary-card">
                <div class="summary-value">${summary.passedStudents}</div>
                <div class="summary-label">Students Passed</div>
            </div>
        </div>

        <!-- Students Performance Table -->
        <table class="students-table">
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Roll No</th>
                    <th>Student Name</th>
                    <th>Admission No</th>
                    <th>Total Marks</th>
                    <th>Obtained</th>
                    <th>Percentage</th>
                    <th>Grade</th>
                    <th>Attendance</th>
                </tr>
            </thead>
            <tbody>
                ${students.map(student => `
                    <tr>
                        <td>${student.rank}</td>
                        <td>${student.rollNumber}</td>
                        <td style="text-align: left;">${student.name}</td>
                        <td>${student.admissionNo}</td>
                        <td>${student.totalMarks}</td>
                        <td>${student.obtainedMarks}</td>
                        <td>${student.percentage.toFixed(1)}%</td>
                        <td class="grade-${student.grade.replace('+', '')}">${student.grade}</td>
                        <td>${student.attendancePercentage.toFixed(1)}%</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>

        <!-- Footer -->
        <div class="footer">
            <p>Generated on ${new Date(generatedAt).toLocaleDateString()} at ${new Date(generatedAt).toLocaleTimeString()}</p>
            <p>This is a computer-generated document.</p>
        </div>
    </div>
</body>
</html>
  `
}

/**
 * Ensure reports directory exists
 */
export function ensureReportsDirectory(): string {
  const reportsDir = path.join(process.cwd(), 'public', 'reports')
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true })
  }
  return reportsDir
}

/**
 * Generate unique filename for report
 */
export function generateReportFilename(
  type: 'report-card' | 'class-report',
  identifier: string,
  termId: string
): string {
  const timestamp = new Date().toISOString().split('T')[0]
  return `${type}-${identifier}-${termId}-${timestamp}.pdf`
}
