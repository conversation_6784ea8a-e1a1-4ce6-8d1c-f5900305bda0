/**
 * React utility functions for safe rendering and key generation
 */

/**
 * Safely filters an array to ensure all items have valid IDs for React keys
 * @param items Array of items to filter
 * @param keyField The field to check for valid keys (defaults to 'id')
 * @returns Filtered array with only items that have valid keys
 */
export function filterValidKeys<T extends Record<string, any>>(
  items: T[], 
  keyField: string = 'id'
): T[] {
  return items.filter(item => item && item[keyField] != null && item[keyField] !== '')
}

/**
 * Generates a safe key for React components
 * @param item The item to generate a key for
 * @param fallbackPrefix Prefix to use if the item doesn't have a valid ID
 * @param index Index to use as fallback
 * @returns A safe key string
 */
export function getSafeKey(
  item: any, 
  fallbackPrefix: string = 'item', 
  index?: number
): string {
  if (item?.id != null && item.id !== '') {
    return String(item.id)
  }
  
  if (index !== undefined) {
    return `${fallbackPrefix}-${index}`
  }
  
  // Generate a random key as last resort
  return `${fallbackPrefix}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Safely maps an array with automatic key filtering and generation
 * @param items Array to map
 * @param mapFn Mapping function
 * @param keyField Field to use for keys (defaults to 'id')
 * @returns Mapped array with safe keys
 */
export function safeMap<T extends Record<string, any>, R>(
  items: T[],
  mapFn: (item: T, index: number) => R,
  keyField: string = 'id'
): R[] {
  return filterValidKeys(items, keyField).map(mapFn)
}

/**
 * Validates that an array of items has unique keys
 * @param items Array to validate
 * @param keyField Field to check for uniqueness
 * @returns Object with validation result and duplicate keys if any
 */
export function validateUniqueKeys<T extends Record<string, any>>(
  items: T[],
  keyField: string = 'id'
): { isValid: boolean; duplicates: any[] } {
  const keys = new Set()
  const duplicates: any[] = []
  
  for (const item of items) {
    const key = item[keyField]
    if (keys.has(key)) {
      duplicates.push(key)
    } else {
      keys.add(key)
    }
  }
  
  return {
    isValid: duplicates.length === 0,
    duplicates
  }
}

/**
 * Console warning for React key issues in development
 * @param componentName Name of the component
 * @param duplicates Array of duplicate keys found
 */
export function warnDuplicateKeys(componentName: string, duplicates: any[]): void {
  if (process.env.NODE_ENV === 'development' && duplicates.length > 0) {
    console.warn(
      `[${componentName}] Found duplicate keys:`,
      duplicates,
      'This may cause React rendering issues.'
    )
  }
}
