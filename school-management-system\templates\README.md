# Student Import Template Guide

## Overview
This template allows you to bulk import student data into the school management system. The template includes all necessary fields for creating student accounts and their guardian information.

## Template Files
- `student-import-template.csv` - CSV format template
- `student-import-template.xlsx` - Excel format template (recommended)

## Required Fields

### Student Information
- **First Name** (Required) - Student's first name
- **Last Name** (Required) - Student's last name  
- **Email** (Required) - Unique email address for student login
- **Date of Birth** (Required) - Format: YYYY-MM-DD (e.g., 2010-06-15)
- **Gender** (Required) - Must be one of: MALE, FEMALE, OTHER

### Contact Information
- **Phone Number** (Optional) - Student's phone number
- **Address** (Optional) - Student's home address

### Guardian Information
- **Guardian Name** (Required) - Primary guardian/parent name
- **Guardian Phone** (Required) - Guardian's contact phone number

### Academic Information
- **Class** (Required) - Class name (e.g., "Grade 8", "Grade 9")
- **Section** (Required) - Section name (e.g., "A", "B", "C")
- **Roll Number** (Optional) - Student's roll number in the class
- **Academic Year** (Required) - Format: YYYY-YYYY (e.g., "2024-2025")
- **Admission Number** (Optional) - If not provided, system will auto-generate

## Data Validation Rules

### Email Format
- Must be a valid email address format
- Must be unique across all users in the system

### Date Format
- Date of Birth must be in YYYY-MM-DD format
- Must be a valid date

### Gender Values
- Only accepts: MALE, FEMALE, OTHER (case-sensitive)

### Class and Section
- Class and Section must exist in the system
- Contact your administrator if you need new classes/sections created

### Phone Numbers
- Can include country codes and formatting
- Examples: +1234567890, (*************, ************

## Import Process

1. **Download Template**: Use the provided CSV or Excel template
2. **Fill Data**: Replace example data with actual student information
3. **Validate Data**: Ensure all required fields are filled and formats are correct
4. **Upload File**: Use the admin dashboard to upload your completed file
5. **Review Results**: Check the import results for any errors or warnings

## Common Errors and Solutions

### "Email already exists"
- Each email must be unique
- Check for duplicate emails in your file
- Verify the email isn't already in the system

### "Class/Section not found"
- Ensure the class and section names match exactly what's in the system
- Contact administrator to create missing classes/sections

### "Invalid date format"
- Use YYYY-MM-DD format only
- Ensure the date is valid (e.g., not February 30th)

### "Invalid gender"
- Use only: MALE, FEMALE, or OTHER
- Check for typos and ensure proper capitalization

## Default Login Credentials

After import, all students will have:
- **Username**: Their email address
- **Default Password**: Student@12345
- Students should change their password on first login

## Support

If you encounter issues with the import process:
1. Check this guide for common solutions
2. Validate your data against the requirements
3. Contact your system administrator for assistance

## Example Data

The template includes sample data to show proper formatting. Replace this with your actual student data before importing.
