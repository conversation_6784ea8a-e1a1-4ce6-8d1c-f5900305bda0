# Page snapshot

```yaml
- generic [active] [ref=e1]:
  - generic [ref=e3]:
    - generic [ref=e4]:
      - heading "Welcome to School Management System" [level=3] [ref=e5]
      - paragraph [ref=e6]: A comprehensive platform for managing school operations
    - generic [ref=e7]:
      - generic [ref=e8]:
        - paragraph [ref=e9]: Please sign in to access your dashboard
        - link "Sign In" [ref=e10] [cursor=pointer]:
          - /url: /login
          - button "Sign In" [ref=e11]
      - generic [ref=e12]:
        - heading "Demo Accounts:" [level=3] [ref=e13]
        - generic [ref=e14]:
          - generic [ref=e15]:
            - strong [ref=e16]: "Admin:"
            - generic [ref=e17]: <EMAIL> / Admin@12345
          - generic [ref=e18]:
            - strong [ref=e19]: "Teacher:"
            - generic [ref=e20]: <EMAIL> / Teacher@12345
          - generic [ref=e21]:
            - strong [ref=e22]: "Student:"
            - generic [ref=e23]: <EMAIL> / Student@12345
  - button "Open Next.js Dev Tools" [ref=e29] [cursor=pointer]:
    - img [ref=e30] [cursor=pointer]
  - alert [ref=e33]
```