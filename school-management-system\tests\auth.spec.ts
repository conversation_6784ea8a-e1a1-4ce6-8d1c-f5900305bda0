import { test, expect } from '@playwright/test'

test.describe('Authentication', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should display login page', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible()
    await expect(page.getByPlaceholder(/email/i)).toBeVisible()
    await expect(page.getByPlaceholder(/password/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible()
  })

  test('should show validation errors for empty fields', async ({ page }) => {
    await page.getByRole('button', { name: /sign in/i }).click()
    
    // Should show validation errors
    await expect(page.getByText(/email is required/i)).toBeVisible()
    await expect(page.getByText(/password is required/i)).toBeVisible()
  })

  test('should show error for invalid credentials', async ({ page }) => {
    await page.getByPlaceholder(/email/i).fill('<EMAIL>')
    await page.getByPlaceholder(/password/i).fill('wrongpassword')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    // Should show error message
    await expect(page.getByText(/invalid credentials/i)).toBeVisible()
  })

  test('should login successfully with valid admin credentials', async ({ page }) => {
    await page.getByPlaceholder(/email/i).fill('<EMAIL>')
    await page.getByPlaceholder(/password/i).fill('admin123')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    // Should redirect to admin dashboard
    await expect(page).toHaveURL(/\/admin/)
    await expect(page.getByText(/admin dashboard/i)).toBeVisible()
  })

  test('should login successfully with valid teacher credentials', async ({ page }) => {
    await page.getByPlaceholder(/email/i).fill('<EMAIL>')
    await page.getByPlaceholder(/password/i).fill('teacher123')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    // Should redirect to teacher dashboard
    await expect(page).toHaveURL(/\/teacher/)
    await expect(page.getByText(/teacher dashboard/i)).toBeVisible()
  })

  test('should login successfully with valid student credentials', async ({ page }) => {
    await page.getByPlaceholder(/email/i).fill('<EMAIL>')
    await page.getByPlaceholder(/password/i).fill('student123')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    // Should redirect to student dashboard
    await expect(page).toHaveURL(/\/student/)
    await expect(page.getByText(/student dashboard/i)).toBeVisible()
  })

  test('should logout successfully', async ({ page }) => {
    // Login first
    await page.getByPlaceholder(/email/i).fill('<EMAIL>')
    await page.getByPlaceholder(/password/i).fill('admin123')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    await expect(page).toHaveURL(/\/admin/)
    
    // Find and click logout button
    await page.getByRole('button', { name: /logout/i }).click()
    
    // Should redirect to login page
    await expect(page).toHaveURL('/')
    await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible()
  })

  test('should redirect unauthenticated users to login', async ({ page }) => {
    await page.goto('/admin/dashboard')
    
    // Should redirect to login
    await expect(page).toHaveURL('/')
    await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible()
  })

  test('should prevent unauthorized access to admin pages', async ({ page }) => {
    // Login as student
    await page.getByPlaceholder(/email/i).fill('<EMAIL>')
    await page.getByPlaceholder(/password/i).fill('student123')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    await expect(page).toHaveURL(/\/student/)
    
    // Try to access admin page
    await page.goto('/admin/dashboard')
    
    // Should be redirected or show unauthorized message
    await expect(page).not.toHaveURL(/\/admin\/dashboard/)
  })

  test('should prevent unauthorized access to teacher pages', async ({ page }) => {
    // Login as student
    await page.getByPlaceholder(/email/i).fill('<EMAIL>')
    await page.getByPlaceholder(/password/i).fill('student123')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    await expect(page).toHaveURL(/\/student/)
    
    // Try to access teacher page
    await page.goto('/teacher/dashboard')
    
    // Should be redirected or show unauthorized message
    await expect(page).not.toHaveURL(/\/teacher\/dashboard/)
  })
})
