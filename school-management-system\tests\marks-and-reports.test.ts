import { test, expect } from '@playwright/test'

test.describe('Marks Management System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3003')
  })

  test('should allow admin to view marks management page', async ({ page }) => {
    // Login as admin
    await page.click('text=Login')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'admin123')
    await page.click('button[type="submit"]')

    // Navigate to marks page
    await page.click('text=Marks')
    
    // Check if marks page loads
    await expect(page.locator('h1')).toContainText('Marks Management')
    
    // Check if filters are present
    await expect(page.locator('select[id="subject"]')).toBeVisible()
    await expect(page.locator('select[id="class"]')).toBeVisible()
  })

  test('should allow teacher to view marks entry page', async ({ page }) => {
    // Login as teacher
    await page.click('text=Login')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'teacher123')
    await page.click('button[type="submit"]')

    // Navigate to marks page
    await page.click('text=Marks')
    
    // Check if marks page loads
    await expect(page.locator('h1')).toContainText('Marks Management')
    
    // Check if exams table is present
    await expect(page.locator('table')).toBeVisible()
  })

  test('should allow student to view their marks', async ({ page }) => {
    // Login as student
    await page.click('text=Login')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'student123')
    await page.click('button[type="submit"]')

    // Navigate to marks page
    await page.click('text=Marks')
    
    // Check if marks page loads
    await expect(page.locator('h1')).toContainText('My Marks')
    
    // Check if filters are present
    await expect(page.locator('select[id="term"]')).toBeVisible()
    await expect(page.locator('select[id="subject"]')).toBeVisible()
  })
})

test.describe('Report Generation System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3003')
  })

  test('should allow admin to access reports management', async ({ page }) => {
    // Login as admin
    await page.click('text=Login')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'admin123')
    await page.click('button[type="submit"]')

    // Navigate to reports page
    await page.click('text=Reports')
    
    // Check if reports page loads
    await expect(page.locator('h1')).toContainText('Reports Management')
    
    // Check if report generation section is present
    await expect(page.locator('text=Generate Report Cards')).toBeVisible()
    
    // Check if filters are present
    await expect(page.locator('select[id="term-select"]')).toBeVisible()
    await expect(page.locator('select[id="class-select"]')).toBeVisible()
  })

  test('should allow teacher to view student reports', async ({ page }) => {
    // Login as teacher
    await page.click('text=Login')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'teacher123')
    await page.click('button[type="submit"]')

    // Navigate to reports page
    await page.click('text=Reports')
    
    // Check if reports page loads
    await expect(page.locator('h1')).toContainText('Student Reports')
    
    // Check if filters are present
    await expect(page.locator('select[id="term"]')).toBeVisible()
    await expect(page.locator('select[id="subject"]')).toBeVisible()
  })

  test('should allow student to view their reports', async ({ page }) => {
    // Login as student
    await page.click('text=Login')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'student123')
    await page.click('button[type="submit"]')

    // Navigate to reports page
    await page.click('text=Reports')
    
    // Check if reports page loads
    await expect(page.locator('h1')).toContainText('My Report Cards')
    
    // Check if report cards section is present
    await expect(page.locator('text=Academic Performance')).toBeVisible()
  })
})

test.describe('API Endpoints', () => {
  test('should protect marks API endpoints with authentication', async ({ request }) => {
    // Test student marks API
    const studentMarksResponse = await request.get('http://localhost:3003/api/student/marks')
    expect(studentMarksResponse.status()).toBe(401) // Unauthorized

    // Test teacher marks API
    const teacherMarksResponse = await request.get('http://localhost:3003/api/teacher/marks')
    expect(teacherMarksResponse.status()).toBe(401) // Unauthorized

    // Test admin marks API
    const adminMarksResponse = await request.get('http://localhost:3003/api/admin/marks')
    expect(adminMarksResponse.status()).toBe(401) // Unauthorized
  })

  test('should protect reports API endpoints with authentication', async ({ request }) => {
    // Test student reports API
    const studentReportsResponse = await request.get('http://localhost:3003/api/student/reports')
    expect(studentReportsResponse.status()).toBe(401) // Unauthorized

    // Test teacher reports API
    const teacherReportsResponse = await request.get('http://localhost:3003/api/teacher/reports')
    expect(teacherReportsResponse.status()).toBe(401) // Unauthorized

    // Test admin reports API
    const adminReportsResponse = await request.get('http://localhost:3003/api/admin/reports')
    expect(adminReportsResponse.status()).toBe(401) // Unauthorized
  })
})
