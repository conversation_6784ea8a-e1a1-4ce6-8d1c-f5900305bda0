import { test, expect } from '@playwright/test'

test.describe('Marks Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login as teacher
    await page.goto('/')
    await page.getByPlaceholder(/email/i).fill('<EMAIL>')
    await page.getByPlaceholder(/password/i).fill('teacher123')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    await expect(page).toHaveURL(/\/teacher/)
  })

  test('should navigate to marks page', async ({ page }) => {
    await page.getByRole('link', { name: /marks/i }).click()
    
    await expect(page).toHaveURL(/\/teacher\/marks/)
    await expect(page.getByText(/marks management/i)).toBeVisible()
  })

  test('should display exams list', async ({ page }) => {
    await page.goto('/teacher/marks')
    
    // Should show exams or empty state
    const hasExams = await page.getByText(/select an exam/i).isVisible()
    const hasEmptyState = await page.getByText(/no exams found/i).isVisible()
    
    expect(hasExams || hasEmptyState).toBe(true)
  })

  test('should allow marks entry for an exam', async ({ page }) => {
    await page.goto('/teacher/marks')
    
    // Check if there are exams available
    const examCards = page.locator('[data-testid="exam-card"]')
    const examCount = await examCards.count()
    
    if (examCount > 0) {
      // Click on first exam
      await examCards.first().click()
      
      // Should show marks entry form
      await expect(page.getByText(/enter marks/i)).toBeVisible()
      
      // Check if there are students to grade
      const studentRows = page.locator('[data-testid="student-row"]')
      const studentCount = await studentRows.count()
      
      if (studentCount > 0) {
        // Enter marks for first student
        const marksInput = studentRows.first().locator('input[placeholder*="marks"]')
        await marksInput.fill('85')
        
        // Enter remarks
        const remarksInput = studentRows.first().locator('input[placeholder*="remarks"]')
        await remarksInput.fill('Good performance')
        
        // Save marks
        await page.getByRole('button', { name: /save.*marks/i }).click()
        
        // Should show success message
        await expect(page.getByText(/marks saved successfully/i)).toBeVisible()
      }
    }
  })

  test('should validate marks input', async ({ page }) => {
    await page.goto('/teacher/marks')
    
    const examCards = page.locator('[data-testid="exam-card"]')
    const examCount = await examCards.count()
    
    if (examCount > 0) {
      await examCards.first().click()
      
      const studentRows = page.locator('[data-testid="student-row"]')
      const studentCount = await studentRows.count()
      
      if (studentCount > 0) {
        // Enter invalid marks (negative)
        const marksInput = studentRows.first().locator('input[placeholder*="marks"]')
        await marksInput.fill('-10')
        
        await page.getByRole('button', { name: /save.*marks/i }).click()
        
        // Should show validation error
        await expect(page.getByText(/cannot be negative/i)).toBeVisible()
      }
    }
  })

  test('should show existing marks for students', async ({ page }) => {
    await page.goto('/teacher/marks')
    
    const examCards = page.locator('[data-testid="exam-card"]')
    const examCount = await examCards.count()
    
    if (examCount > 0) {
      await examCards.first().click()
      
      // Look for students with existing marks
      const gradedBadges = page.locator('text=Graded')
      const gradedCount = await gradedBadges.count()
      
      if (gradedCount > 0) {
        // Should show existing marks in input fields
        const filledInputs = page.locator('input[value]:not([value=""])')
        expect(await filledInputs.count()).toBeGreaterThan(0)
      }
    }
  })
})

test.describe('Student Marks View', () => {
  test.beforeEach(async ({ page }) => {
    // Login as student
    await page.goto('/')
    await page.getByPlaceholder(/email/i).fill('<EMAIL>')
    await page.getByPlaceholder(/password/i).fill('student123')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    await expect(page).toHaveURL(/\/student/)
  })

  test('should navigate to marks page', async ({ page }) => {
    await page.getByRole('link', { name: /marks/i }).click()
    
    await expect(page).toHaveURL(/\/student\/marks/)
    await expect(page.getByText(/my marks/i)).toBeVisible()
  })

  test('should display marks by subject', async ({ page }) => {
    await page.goto('/student/marks')
    
    // Should show subjects or empty state
    const hasSubjects = await page.getByText(/select a subject/i).isVisible()
    const hasEmptyState = await page.getByText(/no marks found/i).isVisible()
    
    expect(hasSubjects || hasEmptyState).toBe(true)
  })

  test('should filter marks by term', async ({ page }) => {
    await page.goto('/student/marks')
    
    // Check if term filter exists
    const termSelect = page.locator('select[id*="term"]')
    const termExists = await termSelect.isVisible()
    
    if (termExists) {
      // Select a term
      await termSelect.selectOption({ index: 1 })
      
      // Should update the marks display
      await page.waitForTimeout(1000) // Wait for data to load
    }
  })

  test('should show marks details when clicking on subject', async ({ page }) => {
    await page.goto('/student/marks')
    
    const subjectCards = page.locator('[data-testid="subject-card"]')
    const subjectCount = await subjectCards.count()
    
    if (subjectCount > 0) {
      await subjectCards.first().click()
      
      // Should show detailed marks
      await expect(page.getByText(/exam details/i)).toBeVisible()
    }
  })
})
