import { describe, it, expect } from 'vitest'
import { buildSettingsUpdates } from '../src/app/api/admin/settings/route'

describe('buildSettingsUpdates', () => {
  it('builds general settings mapping from UI payload (uses name, not schoolName)', () => {
    const data = {
      name: 'Relish Infotech',
      address: '123 Education Street, City, State 12345',
      phone: '+****************',
      email: '<EMAIL>',
      website: 'www.advanceschool.edu',
      principal: '<PERSON><PERSON><PERSON>',
      establishedYear: '1995',
    }

    const updates = buildSettingsUpdates('general', data)

    const map = Object.fromEntries(updates.map(u => [u.key, u.value]))
    expect(map['school_name']).toBe('Relish Infotech')
    expect(map['school_address']).toBe(data.address)
    expect(map['school_phone']).toBe(data.phone)
    expect(map['school_email']).toBe(data.email)
    expect(map['school_website']).toBe(data.website)
    expect(map['school_principal']).toBe(data.principal)
    expect(map['school_established_year']).toBe(data.establishedYear)
  })

  it('throws when a required field is missing', () => {
    const badData = {
      // name missing
      address: '123',
      phone: '1',
      email: '<EMAIL>',
      website: 'x',
      principal: 'y',
      establishedYear: '2000',
    } as any

    expect(() => buildSettingsUpdates('general', badData)).toThrow()
  })
})

