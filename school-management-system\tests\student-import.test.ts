import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import * as XLSX from 'xlsx';

describe('Student Import Functionality', () => {
  const templatesDir = path.join(process.cwd(), 'templates');
  const excelTemplatePath = path.join(templatesDir, 'student-import-template.xlsx');
  const csvTemplatePath = path.join(templatesDir, 'student-import-template.csv');

  beforeAll(() => {
    // Ensure templates directory exists
    if (!fs.existsSync(templatesDir)) {
      fs.mkdirSync(templatesDir, { recursive: true });
    }
  });

  describe('Template Files', () => {
    it('should have Excel template file', () => {
      expect(fs.existsSync(excelTemplatePath)).toBe(true);
    });

    it('should have CSV template file', () => {
      expect(fs.existsSync(csvTemplatePath)).toBe(true);
    });

    it('should have valid Excel template structure', () => {
      if (fs.existsSync(excelTemplatePath)) {
        const workbook = XLSX.readFile(excelTemplatePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        // Check headers
        const headers = data[0] as string[];
        const expectedHeaders = [
          'First Name',
          'Last Name',
          'Email',
          'Date of Birth',
          'Gender',
          'Phone Number',
          'Address',
          'Guardian Name',
          'Guardian Phone',
          'Class',
          'Section',
          'Roll Number',
          'Academic Year',
          'Admission Number'
        ];

        expect(headers).toEqual(expectedHeaders);
        expect(data.length).toBeGreaterThan(1); // Should have example data
      }
    });

    it('should have valid CSV template structure', () => {
      if (fs.existsSync(csvTemplatePath)) {
        const csvContent = fs.readFileSync(csvTemplatePath, 'utf-8');
        const lines = csvContent.split('\n').filter(line => line.trim());
        
        expect(lines.length).toBeGreaterThan(1); // Should have header and example data
        
        const headers = lines[0].split(',');
        expect(headers).toContain('First Name');
        expect(headers).toContain('Last Name');
        expect(headers).toContain('Email');
        expect(headers).toContain('Date of Birth');
        expect(headers).toContain('Gender');
        expect(headers).toContain('Guardian Name');
        expect(headers).toContain('Guardian Phone');
        expect(headers).toContain('Class');
        expect(headers).toContain('Section');
        expect(headers).toContain('Academic Year');
      }
    });
  });

  describe('Data Validation', () => {
    it('should validate required fields', () => {
      const requiredFields = [
        'First Name',
        'Last Name',
        'Email',
        'Date of Birth',
        'Gender',
        'Guardian Name',
        'Guardian Phone',
        'Class',
        'Section',
        'Academic Year'
      ];

      // This would be tested in the actual API endpoint
      expect(requiredFields.length).toBe(10);
    });

    it('should validate date format', () => {
      const validDate = '2010-06-15';
      const invalidDate = '15/06/2010';
      
      // Test date format validation
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      expect(dateRegex.test(validDate)).toBe(true);
      expect(dateRegex.test(invalidDate)).toBe(false);
    });

    it('should validate academic year format', () => {
      const validAcademicYear = '2024-2025';
      const invalidAcademicYear = '2024';
      
      const academicYearRegex = /^\d{4}-\d{4}$/;
      expect(academicYearRegex.test(validAcademicYear)).toBe(true);
      expect(academicYearRegex.test(invalidAcademicYear)).toBe(false);
    });

    it('should validate gender values', () => {
      const validGenders = ['MALE', 'FEMALE', 'OTHER'];
      const invalidGender = 'male';
      
      expect(validGenders.includes('MALE')).toBe(true);
      expect(validGenders.includes('FEMALE')).toBe(true);
      expect(validGenders.includes('OTHER')).toBe(true);
      expect(validGenders.includes(invalidGender)).toBe(false);
    });
  });

  describe('File Processing', () => {
    it('should support Excel file extensions', () => {
      const validExtensions = ['.xlsx', '.xls'];
      const testFiles = ['test.xlsx', 'test.xls', 'test.csv', 'test.txt'];
      
      const isValidExcelFile = (fileName: string) => {
        return validExtensions.some(ext => fileName.toLowerCase().endsWith(ext));
      };

      expect(isValidExcelFile('test.xlsx')).toBe(true);
      expect(isValidExcelFile('test.xls')).toBe(true);
      expect(isValidExcelFile('test.csv')).toBe(false);
      expect(isValidExcelFile('test.txt')).toBe(false);
    });

    it('should support CSV file extension', () => {
      const isValidCsvFile = (fileName: string) => {
        return fileName.toLowerCase().endsWith('.csv');
      };

      expect(isValidCsvFile('test.csv')).toBe(true);
      expect(isValidCsvFile('test.xlsx')).toBe(false);
    });
  });
});
