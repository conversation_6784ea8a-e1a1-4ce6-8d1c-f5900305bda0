{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"DATABASE_URL": "@database_url", "NEXTAUTH_URL": "@nextauth_url", "NEXTAUTH_SECRET": "@nextauth_secret", "SMTP_HOST": "@smtp_host", "SMTP_PORT": "@smtp_port", "SMTP_USER": "@smtp_user", "SMTP_PASSWORD": "@smtp_password", "SMTP_SECURE": "@smtp_secure"}, "build": {"env": {"DATABASE_URL": "@database_url", "NEXTAUTH_URL": "@nextauth_url", "NEXTAUTH_SECRET": "@nextauth_secret"}}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "regions": ["iad1"], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}